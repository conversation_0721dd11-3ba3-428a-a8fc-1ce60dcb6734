{"$schema": "https://repomix.com/schemas/latest/schema.json", "input": {"maxFileSize": 1428800}, "output": {"filePath": "repomix-output.txt", "style": "plain", "parsableStyle": false, "fileSummary": true, "directoryStructure": true, "files": true, "removeComments": false, "removeEmptyLines": false, "compress": false, "topFilesLength": 5, "showLineNumbers": false, "copyToClipboard": false, "git": {"sortByChanges": true, "sortByChangesMaxCommits": 100, "includeDiffs": false}}, "include": [], "ignore": {"useGitignore": true, "useDefaultPatterns": true, "customPatterns": []}, "security": {"enableSecurityCheck": true}, "tokenCount": {"encoding": "o200k_base"}}