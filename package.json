{"name": "aider-desk", "version": "0.20.1-dev", "description": "Aider desktop application wrapper", "main": "./out/main/index.js", "author": "Hotovo", "homepage": "https://www.hotovo.com", "repository": {"type": "git", "url": "https://github.com/hotovo/aider-desk.git"}, "scripts": {"format": "prettier --write .", "lint": "eslint src --fix", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false", "typecheck:mcp": "tsc --noEmit -p tsconfig.mcp-server.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web && npm run typecheck:mcp", "start": "electron-vite preview", "dev": "electron-vite dev", "dev:no-hmr": "NO_HMR=true electron-vite dev", "build:mcp": "esbuild src/mcp-server/aider-desk-mcp-server.ts --bundle --platform=node --outdir=out/mcp-server", "build": "npm run typecheck && electron-vite build && npm run build:mcp", "postinstall": "electron-builder install-app-deps && node scripts/download-uv.mjs && patch-package", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:mac": "npm run build && electron-builder --mac", "build:linux": "npm run build && electron-builder --linux", "prepare": "husky"}, "dependencies": {"@ai-sdk/amazon-bedrock": "^2.2.10", "@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/deepseek": "^0.2.14", "@ai-sdk/google": "^1.2.19", "@ai-sdk/openai": "^1.3.22", "@aws-crypto/sha256-js": "^5.0.0", "@aws-sdk/credential-provider-node": "^3.772.0", "@aws-sdk/credential-providers": "^3.782.0", "@aws-sdk/types": "^3.540.0", "@buger/probe": "^0.6.0-rc10", "@codemirror/autocomplete": "^6.18.6", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dotenvx/dotenvx": "^1.24.5", "@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^3.0.0", "@headlessui/react": "^2.2.0", "@n8n/json-schema-to-zod": "^1.1.0", "@openrouter/ai-sdk-provider": "^0.7.1", "@replit/codemirror-vim": "^6.3.0", "@requesty/ai-sdk": "^0.0.9", "@smithy/eventstream-codec": "^2.0.0", "@smithy/protocol-http": "^3.3.0", "@smithy/signature-v4": "^2.0.0", "@smithy/util-utf8": "^2.0.0", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@uiw/codemirror-theme-github": "^4.23.12", "@uiw/react-codemirror": "^4.23.12", "ai": "^4.3.16", "babel-plugin-react-compiler": "^19.1.0-rc.2", "better-sqlite3": "^12.0.0", "cheerio": "^1.0.0", "clsx": "^2.1.1", "cors": "^2.8.5", "electron-store": "^10.0.0", "electron-updater": "^6.1.7", "express": "^4.21.2", "fix-path": "^4.0.0", "focus-trap-react": "^11.0.3", "framer-motion": "^11.18.2", "glob": "^11.0.2", "gpt-tokenizer": "^2.9.0", "html-to-image": "^1.11.13", "i18next": "^24.2.3", "i18next-browser-languagedetector": "^8.0.4", "ignore": "^5.3.2", "js-tiktoken": "^1.0.20", "lodash": "^4.17.21", "mark.js": "^8.11.1", "match-sorter": "^8.0.0", "object-hash": "^3.0.0", "ollama-ai-provider": "^1.2.0", "os-name": "^6.0.0", "patch-package": "^8.0.0", "playwright-core": "^1.49.0", "posthog-node": "^4.18.0", "prismjs": "^1.29.0", "react-compiler-runtime": "^19.1.0-rc.2", "react-complex-tree": "^2.4.5", "react-country-flag": "^3.1.0", "react-datepicker": "^8.4.0", "react-diff-view": "^3.3.1", "react-hotkeys-hook": "^4.6.1", "react-i18next": "^15.4.1", "react-icons": "^5.5.0", "react-remark": "^2.1.0", "react-resizable": "^3.0.5", "react-router-dom": "^7.0.1", "react-textarea-autosize": "^8.5.6", "react-toastify": "^10.0.6", "react-tooltip": "^5.28.0", "react-use": "^17.5.1", "recharts": "^3.0.2", "refractor": "^3.6.0", "rehype-autolink-headings": "^7.1.0", "rehype-slug": "^6.0.0", "simple-git": "^3.27.0", "socket.io": "^4.8.1", "textarea-caret": "^3.1.0", "tmp": "^0.2.3", "tree-kill": "^1.2.2", "unidiff": "^1.0.4", "uuid": "^11.0.2", "vite-plugin-prismjs": "^0.0.11", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "ws": "^8.18.0", "yaml": "^2.8.0", "yaml-front-matter": "^4.1.1", "zod": "^3.24.2"}, "devDependencies": {"@electron-toolkit/tsconfig": "^1.0.1", "@electron/notarize": "^2.5.0", "@eslint/js": "^9.18.0", "@modelcontextprotocol/sdk": "^1.7.0", "@tailwindcss/typography": "^0.5.16", "@types/better-sqlite3": "^7.6.13", "@types/lodash": "^4.17.17", "@types/node": "^20.14.8", "@types/object-hash": "^3.0.6", "@types/prismjs": "^1.26.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-resizable": "^3.0.8", "@types/refractor": "^3.4.1", "@types/socket.io": "^3.0.1", "@types/textarea-caret": "^3.0.3", "@types/tmp": "^0.2.6", "@types/yaml-front-matter": "^4.1.3", "@vitejs/plugin-react": "^4.3.1", "adm-zip": "^0.5.16", "autoprefixer": "^10.4.20", "axios": "^1.8.3", "electron": "^35.1.5", "electron-builder": "^26.0.12", "electron-vite": "^2.3.0", "esbuild": "^0.25.1", "eslint": "^9.18.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.7.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^6.0.0-rc.1", "globals": "^15.9.0", "husky": "^9.1.7", "lint-staged": "^15.4.1", "node-fetch": "^3.3.2", "prettier": "^3.3.2", "react": "^18.3.1", "react-dom": "^18.3.1", "sass-embedded": "^1.89.0", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.4.14", "tar": "^7.4.3", "typescript": "^5.5.2", "typescript-eslint": "^8.21.0", "vite": "^5.4.19", "vite-tsconfig-paths": "^5.1.0"}, "lint-staged": {"*.{ts,tsx}": "eslint --fix"}}