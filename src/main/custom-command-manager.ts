import fs from 'fs';
import path from 'path';
import os from 'os';

import { loadFront } from 'yaml-front-matter';

import type { CustomCommand } from '@common/types';

import { AIDER_DESK_COMMANDS_DIR } from '@/constants';
import logger from '@/logger';
import { Project } from '@/project';

export class CustomCommandManager {
  constructor(private readonly project: Project) {}

  private loadAllCommands(): Map<string, CustomCommand> {
    const commands: Map<string, CustomCommand> = new Map();

    // Load global commands from HOME directory first
    const homeDir = os.homedir();
    const globalCommandsDir = path.join(homeDir, AIDER_DESK_COMMANDS_DIR);
    this.loadCommandsFromDir(globalCommandsDir, commands);

    // Load project-specific commands (these will overwrite global ones with same name)
    const projectCommandsDir = path.join(this.project.baseDir, AIDER_DESK_COMMANDS_DIR);
    this.loadCommandsFromDir(projectCommandsDir, commands);

    return commands;
  }

  private loadCommandsFromDir(commandsDir: string, commands: Map<string, CustomCommand>) {
    if (!fs.existsSync(commandsDir)) {
      return;
    }

    try {
      const files = fs.readdirSync(commandsDir).filter((f) => f.endsWith('.md'));
      for (const file of files) {
        this.loadCommandFile(path.join(commandsDir, file), commands);
      }
    } catch (err) {
      logger.error(`[CustomCommandManager] Failed to read commands directory ${commandsDir}: ${err}`);
    }
  }

  private loadCommandFile(filePath: string, commands: Map<string, CustomCommand>) {
    try {
      const content = fs.readFileSync(filePath, 'utf-8');
      const parsed = loadFront(content);
      if (!parsed.description) {
        throw new Error('Missing description in frontmatter');
      }
      const name = path.basename(filePath, '.md');
      const args = Array.isArray(parsed.arguments) ? parsed.arguments : [];
      const template = parsed.__content?.trim() || '';
      commands.set(name, { name, description: parsed.description, arguments: args, template });
    } catch (err) {
      logger.error(`[CustomCommandManager] Failed to parse ${filePath}: ${err}`);
      // Optionally: send error to chat window via IPC or callback
    }
  }

  getCommand(name: string): CustomCommand | undefined {
    const commands = this.loadAllCommands();
    return commands.get(name);
  }

  getAllCommands(): CustomCommand[] {
    const commands = this.loadAllCommands();
    return Array.from(commands.values());
  }
}
