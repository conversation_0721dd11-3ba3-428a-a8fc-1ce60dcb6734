{"languages": {"en": "English", "zh": "中文"}, "common": {"settings": "Settings", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "add": "Add", "remove": "Remove", "close": "Close", "confirm": "Confirm", "back": "Back", "next": "Next", "finish": "Finish", "loading": "Loading...", "startingUp": "Starting up...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info", "yes": "Yes", "no": "No", "open": "Open", "openProject": "Open Project", "lock": "Lock", "default": "<PERSON><PERSON><PERSON>", "suffix.billion": "B", "suffix.million": "M", "suffix.thousand": "K", "ok": "OK"}, "search": {"placeholder": "Search...", "previousResult": "Previous result (SHIFT + F3)", "nextResult": "Next result (F3)"}, "onboarding": {"title": "Welcome to <PERSON><PERSON>", "description": "Aid<PERSON>k is your desktop companion for AI-assisted coding. This application brings the power of <PERSON><PERSON>'s AI coding assistant to a user-friendly interface, helping you:", "skipForNow": "Skip for now", "finish": "Finish Setup", "features": {"1": "Manage multiple coding projects", "2": "Track your AI usage and costs", "3": "Interact with AI models in a structured way", "4": "Visualize and manage your code files", "5": "Use agentic flow for automated coding tasks and connection to MCP servers"}, "getStarted": "Let's get started by configuring your Aider settings.", "steps": {"welcome": "Welcome", "connectModel": "Connect AI Model", "language": "Language", "aider": "Aider", "agent": "AI Agent", "providers": "Providers", "finish": "Finish"}, "language": {"title": "Choose Your Language", "description": "Select your preferred language for the application interface."}, "aider": {"title": "Configure Aider", "description": "To get started, please configure your Aider settings. You'll need to:", "options": {"1": "Add your API keys for the LLM provider you want to use", "2": "Set any additional options for Aider", "3": ".env and .aider.conf.yaml are also read as they would with <PERSON>er"}, "configureLater": "You can also do that later in the Settings.", "fineTuneTitle": "Fine-<PERSON><PERSON> (Optional)", "fineTuneDescription": "Advanced users can configure additional Aider settings and environment variables. This step is completely optional and can be safely skipped.", "fineTuneNote": "Note: These settings are for advanced users who want to customize <PERSON><PERSON>'s behavior. Most users can skip this step and configure these later in Settings if needed."}, "providers": {"title": "Model Providers", "description": "Configure your LLM providers for use with both Aider and Agent modes. You can set up API keys and additional settings for various providers.", "configureLater": "You can configure these later in the Settings if you prefer.", "connectTitle": "Connect Your AI Model", "connectDescription": "Aider Desk supports multiple AI providers including OpenAI, Anthropic, Google Gemini, and more. Your API keys are stored securely on your local machine and never shared.", "advancedUsersNote": "Advanced users: You can also configure providers using environment variables in the next step's Aider configuration section."}, "agent": {"title": "Meet Your AI Agent", "description": "Discover the power of autonomous AI assistance with <PERSON><PERSON>'s Agent mode. Your AI agent can work independently to complete complex tasks.", "capabilities": "Agent Capabilities:", "autonomousPlanning": "Autonomous Task Planning:", "autonomousPlanningDesc": "Breaks down complex requests into manageable steps and executes them systematically.", "toolUse": "Tool Use:", "toolUseDesc": "Leverages various tools for file operations, web searches, and code analysis.", "extensible": "Extensible via MCP:", "extensibleDesc": "Connect to Model Context Protocol servers for enhanced capabilities.", "configureAgent": "Configure Agent", "finishLater": "Finish Setup (Configure Agent Later)", "configureTitle": "Configure Your Agent (Optional)", "configureDescription": "Customize your default agent profile to match your workflow. You can adjust settings like tool permissions, context preferences, and behavior rules."}, "complete": {"title": "Setup Complete!", "description": "Congratulations! You've successfully set up Aider Desk. You're now ready to start coding with AI assistance.", "ready": "Click 'Finish Setup' to begin your coding journey.", "success": "Onboarding completed successfully! Welcome to Aider Desk."}, "errors": {"navigationFailed": "Navigation failed. Please try again.", "finishFailed": "Failed to complete onboarding. Please try again."}}, "noProjectsOpen": {"welcome": "Welcome to", "description": "To begin, open a project directory. This will enable Aid<PERSON> to work with your code."}, "home": {"title": "Home", "noProjectsOpen": "No Projects Open", "openProject": "Open Project", "addFile": "Add File", "projectView": "Project View", "recentProjects": "Recent projects"}, "tips": {"multipleProjects": "Tip: You can open multiple projects and switch between them using the tabs at the top (or CTRL+Tab)."}, "mcpServer": {"addServer": "Add MCP Server", "editServer": "Edit MCP Server: {{name}}", "serverConfigJson": "Server Config JSON", "pasteServerAs": "Paste your server as:\n\n{{example}}\n\nor\n\n{{exampleNoParent}}\n\nor\n\n{{exampleBare}}", "invalidConfig": "Invalid configuration", "emptyConfig": "Configuration cannot be empty", "configHint": "You can add multiple servers at once by pasting a JSON object with multiple server configurations.\n\nYou can use ${projectDir} in env and args to reference the current project directory.", "viewExamples": "View Examples"}, "model": {"label": "Model", "labelMultiple": "Models", "selectLabel": "Model", "placeholder": "e.g. anthropic/claude-sonnet-4-20250514"}, "openai": {"apiKey": "API Key", "baseUrl": "Base URL", "modelName": "Model Name", "baseUrlPlaceholder": "e.g. http://localhost:8080/v1"}, "ollama": {"baseUrl": "Base URL", "baseUrlPlaceholder": "e.g. http://localhost:11434/api"}, "anthropic": {"apiKey": "API Key"}, "gemini": {"apiKey": "API Key", "customBaseUrl": "Custom Base URL", "thinkingBudget": "Thinking budget (tokens)", "includeThoughts": "Include thoughts", "includeThoughtsTooltip": "When enabled, the model's internal thought process will be included in the conversation.", "useSearchGrounding": "Use Search grounding", "useSearchGroundingTooltip": "When enabled, the model will use search results to ground its responses, improving accuracy and relevance."}, "deepseek": {"apiKey": "API Key"}, "bedrock": {"apiKey": "API Key", "region": "Region", "regionPlaceholder": "e.g. us-east-1 or AWS_REGION env", "accessKeyId": "Access Key ID", "accessKeyIdTooltip": "AWS access key ID. Can be left empty if AWS_PROFILE is set in environment.", "secretAccessKey": "Secret Access Key", "secretAccessKeyTooltip": "AWS secret access key. Can be left empty if AWS_PROFILE is set in environment.", "sessionToken": "Session Token", "sessionTokenTooltip": "Optional session token for temporary credentials."}, "openRouter": {"apiKey": "API Key", "modelPlaceholder": "e.g. anthropic/claude-3.7-sonnet"}, "requesty": {"apiKey": "API Key", "modelPlaceholder": "e.g. claude-4-sonnet", "autoCacheLabel": "Use Auto Cache", "autoCacheTooltip": "This will instruct the router to attempt to cache the response from the provider. If a similar request has been cached previously, it might be served from the cache (depending on the provider’s caching strategy and TTL)."}, "providers": {"anthropic": "Anthropic", "bedrock": "Bedrock", "deepseek": "Deepseek", "gemini": "Gemini", "ollama": "Ollama", "openai": "OpenAI", "openai-compatible": "OpenAI Compatible", "openrouter": "OpenRouter", "requesty": "Requesty"}, "select": {"placeholder": "Select an option", "loadingSessions": "Loading sessions..."}, "settings": {"title": "Settings", "tabs": {"general": "General", "providers": "Providers", "aider": "Aider", "agent": "Agent", "about": "About"}, "gui": "Appearance", "startup": {"title": "Start Up", "emptySession": "Start with empty session", "lastSession": "Load last session", "specificSession": "Load specific session"}, "language": "Language", "notifications": {"title": "Notifications"}, "agent": {"mcpServers": "MCP Servers", "provider": "Provider", "providerSettings": "Provider <PERSON>s", "context": "Context", "runSettings": "Parameters", "maxIterations": "Max Iterations", "minTimeBetweenToolCalls": "Min Time Between Tool Calls (ms)", "maxTokens": "<PERSON>", "temperature": "Temperature", "customInstructions": "Additional Rules", "rules": "Rules", "rulesInfo": "You can use Rules to add specific instructions for specific MCP servers or to control flow of the agent.", "ruleFilesInfo": "Any file in your project's <file>.aider-desk/rules</file> directory is automatically included in the Rules. See example <a>here</a>.", "noServersConfigured": "No MCP servers configured.", "computationalResources": "Maximum number of iterations for the agent per run. Helps control computational resources.", "rateLimiting": "Sets the minimum time between tool calls to prevent rate limiting (e.g. for Brave or other API-constrained services).", "temperatureTooltip": "Controls randomness in responses. Lower values (0.0-0.3) make responses more focused and deterministic, higher values (0.7-1.0) make them more creative and varied.", "tokensPerResponse": "Maximum number of tokens the agent can use per response.", "addMcpServer": "Add", "editConfig": "Edit Config", "editMcpServersConfig": "Edit MCP Servers Config", "reloadServers": "Reload", "powerToolsTooltip": "Allows the agent to use tools for direct file operations (read, write, edit), searching (glob, grep, semantic search), and executing shell commands (bash).", "aiderToolsTooltip": "Agent can use Aider to perform add/drop context files actions and run prompts.", "useAiderTools": "Use Aider tools", "usePowerTools": "Use Power tools", "useTodoTools": "Use Todo tools", "includeContextFiles": "Include context files", "includeFilesTooltip": "Adds content of context files into the chat of agent. This will increase token usage.", "includeRepoMap": "Include repository map", "includeRepoMapTooltip": "Include the repository map from <PERSON><PERSON> in the agent's context. This can help the agent understand the project structure, but will increase token usage.", "autoApprove": "Auto approve", "selectOrCreateProfile": "Select or create new agent profile", "newProfileName": "New Profile", "envVarPlaceholder": "{{envVar}} env var is used if left empty", "envVarFoundPlaceholder": "Found in: {{source}}", "createNewProfile": "Create New", "genericTools": "Tools", "noGenericToolsConfigured": "No tools configured.", "todoTools": "Todo tools", "includeTodoToolsTooltip": "Allows the agent to manage a list of todo items, including setting, getting, updating, and clearing them."}, "aider": {"options": "Options", "optionsPlaceholder": "e.g. --no-auto-commits --cache-prompts", "autoCommits": "Auto commit of LLM changes", "cachingEnabled": "Enable prompt caching", "watchFiles": "Watch files for changes", "environmentVariables": "Environment Variables", "envVarsPlaceholder": "#################\n# LLM parameters:\n#\n# Include xxx_API_KEY parameters and other params needed for your LLMs.\n# See https://aider.chat/docs/llms.html for details.\n\n## OpenAI\n#OPENAI_API_KEY=\n\n## Anthropic\n#ANTHROPIC_API_KEY=", "optionsDocumentation": "Check the documentation for available options at", "envVarsDocumentation": "Check the documentation for environment variables at", "context": "Context", "addRuleFiles": "Add rule files from project's <file>.aider-desk/rules</file> directory as read-only files."}, "common": {"showSecrets": "Show Secrets"}, "notificationsEnabled": "Notifications enabled", "zoom": "Zoom level", "theme": "Theme", "themeOptions": {"dark": "Dark", "light": "Light"}, "aiderRestartConfirm": {"title": "<PERSON><PERSON> Required", "message": "Changes to Aider options or environment variables require a restart of the Aider session to take effect. Would you like to restart now?", "restartNow": "Restart now", "later": "I'll do it later"}, "models": {"title": "Model Providers", "description": "Configure your LLM providers for use with both Aider and Agent modes. Click on a provider card to configure API key and additional settings.", "configured": "Configured", "notConfigured": "Not configured", "agentOnly": "Agent only", "showAllProviders": "Show All Providers", "additionalProvidersInfo": "You can configure additional LLM providers for Aider by setting environment variables in the <aiderTab>Aider settings</aiderTab>."}, "about": {"version": "Version", "notAvailable": "N/A", "checkForUpdates": "Check for updates", "newAiderVersionAvailable": "New Aider version ({{version}}) is available - restart AiderDesk to install.", "updateAvailable": "Update is available", "downloadUpdate": "Download", "downloadingUpdate": "Downloading update", "newAiderDeskVersionReady": "A new version is ready. Restart AiderDesk to apply the update.", "downloadAutomatically": "Download update automatically", "releaseNotes": "Release notes", "openLogsDirectory": "Open logs directory", "openLogsError": "Error opening a log directory"}, "telemetry": {"title": "Telemetry"}, "promptBehavior": {"title": "Prompt Behavior", "showSuggestions": "Show suggestions", "automaticallyWhileTyping": "Automatically while typing", "onlyWhenTabPressed": "Only when TAB key is pressed", "suggestionsDelay": "Suggestions delay", "requireCommandConfirmation": "Require confirmation for commands", "addCommand": "/add", "readOnlyCommand": "/read-only", "modelCommand": "/model", "modeSwitchingCommands": "Mode commands (/code, /agent, ...)", "keyBindings": "Key bindings", "useVimBindings": "Use Vim bindings", "useVimBindingsTooltip": "Enable Vim key bindings for navigating and editing text in the prompt field.", "modeAtSign": "@ mention mode"}}, "messages": {"thinking": "Thinking...", "loading": "Loading...", "copy": "Copy", "copied": "Copied!", "interrupted": "Interrupted by user.", "delete": "Delete", "redo": "Redo", "edit": "Edit", "editingLastMessage": "Editing last message", "cancelEdit": "Cancel"}, "errors": {"frozenTitle": "AIDER FROZEN?", "frozenMessage": "Aider process seems to be frozen. Would you like to restart the session?"}, "dialogs": {"confirm": "Confirm", "confirmDelete": "Are you sure you want to delete this?", "addFile": "Add File", "openProject": "Open Project", "recentProjects": "Recent projects", "openProjectTitle": "OPEN PROJECT", "projectPathPlaceholder": "Type the path to your project or use icon to browse", "openButton": "Open", "browseFoldersTooltip": "Browse folders", "projectAlreadyOpenWarning": "This project is already open."}, "mode": {"code": "Code", "ask": "Ask", "architect": "Architect", "context": "Context", "agent": "Agent"}, "addFileDialog": {"title": "Add context files", "browse": "Browse files", "readOnly": "Read-Only"}, "fileFinder": {"addPathTooltip": "Add path", "browseFile": "Browse for files", "browseDirectory": "Browse for directories", "placeholder": {"filesOnly": "Enter file to add", "directoriesOnly": "Enter directory to add", "filesAndDirectories": "Enter file or directory to add"}, "placeholderFinish": {"filesOnly": "Enter file or press Enter to finish", "directoriesOnly": "Enter directory or press Enter to finish", "filesAndDirectories": "Enter file or directory or press Enter to finish"}}, "fileChip": {"removeFileTooltip": "Remove file"}, "contextFiles": {"title": "Context Files", "add": "Add context files", "addFileTooltip": "Add file (use Ctrl/Cmd to add as Read-only)", "addFileTooltip.ctrl": "Add file (use Ctrl to add as Read-only)", "addFileTooltip.cmd": "Add file (use Cmd to add as Read-only)", "readOnly": "Read-only file", "showAllFiles": "Show all files", "hideAllFiles": "Hide all files", "expandAll": "Expand all", "collapseAll": "Collapse all", "dropAll": "Drop all context files"}, "modelSelector": {"mainModel": "Main model", "editorModel": "Editor model", "weakModel": "Weak model", "architectModel": "Architect model", "agentModel": "Agent model", "reasoning": "Reasoning", "thinkingTokens": "Thinking tokens", "loadingModel": "Loading model...", "searchPlaceholder": "Search models or enter custom name...", "removePreferred": "Remove from preferred models", "invalidModelSelection": "Invalid format of the model. Expected format is 'provider/model', e.g. 'openai/gpt-4'.", "providerNotSupported": "Provider '{{provider}}' is not supported. Supported providers are: {{providers}}."}, "modelInfo": {"noInfo": "No model information available.", "maxInputTokens": "Max Input Tokens", "maxOutputTokens": "<PERSON> Output Tokens", "inputCostPerMillion": "Input Cost / 1M Tokens", "outputCostPerMillion": "Output Cost / 1M Tokens"}, "mcp": {"serverToolStatus": "{{enabledCount}} of {{count}} tools enabled", "toolsCount_one": "{{count}} tool", "toolsCount_other": "{{count}} tools", "loadToolsError": "Failed to load MCP server tools", "noToolsFound": "No tools could be found. Check your configuration.", "servers": "MCP servers", "tools": "Tools"}, "tasks": {"title": "Tasks", "addTodo": "Add <PERSON>", "editTodo": "<PERSON>", "deleteTodo": "Delete Todo", "todoName": "Todo Name", "todoNamePlaceholder": "Enter todo name...", "saveTodo": "Save", "cancelTodo": "Cancel", "noTodos": "No todo items", "confirmDelete": "Are you sure you want to delete this todo item?"}, "tool": {"noDescription": "No description", "approval": {"never": "Never", "always": "Always", "ask": "Ask"}}, "autocompletion": {"placeholder": "Type to search..."}, "session": {"edit": "Edit Session", "save": "Save Session", "name": "Session Name", "namePlaceholder": "Enter session name", "loadSettings": "Load Settings", "loadMessages": "Load messages from this session", "loadFiles": "Load context files from this session"}, "costInfo": {"files": "Files", "repoMap": "Repo map", "refreshRepoMap": "Refresh repository map", "messages": "Messages", "clearMessages": "Clear message history", "agent": "Agent", "aider": "Aider", "total": "Total", "restartSession": "Restart session", "tokenUsage": "{{usedTokens}} of {{maxTokens}}"}, "sessions": {"title": "Sessions", "empty": "No saved sessions", "loadMessages_one": "Load {{count}} message", "loadMessages_other": "Load {{count}} messages", "loadFiles_one": "Load {{count}} file", "loadFiles_other": "Load {{count}} files", "saveAsNew": "Save as new", "exportAsMarkdown": "Export as <PERSON><PERSON>", "exportAsImage": "Export as Image", "deleteTitle": "Delete Session", "deleteConfirm": "Are you sure you want to delete the session \"{{name}}\"?", "deleteWarning": "This action cannot be undone.", "save": "Save session", "add": "Add new session", "newSessionPlaceholder": "Enter session name", "sessionSaved": "Session saved."}, "commandOutput": {"command": "Command"}, "reflectedMessage": {"title": "Reflected Message"}, "responseMessage": {"inputTokens": "Input Tokens", "outputTokens": "Output Tokens", "cacheWriteTokens": "<PERSON><PERSON>", "cacheReadTokens": "<PERSON><PERSON>"}, "thinkingAnswer": {"thinking": "THINKING", "answer": "ANSWER"}, "toolMessage": {"toolLabel": "{{server}}: {{tool}}", "arguments": "Arguments:", "executing": "Tool is executing...", "result": "Result:", "error": "Error while executing tool:", "denied": "Denied by user.", "deniedByReason": "Denied by user:\n{{reason}}", "aider": {"addContextFiles": "Adding file(s) to <PERSON><PERSON>'s context:\n{{paths}}", "dropContextFiles": "Dropping file(s) from <PERSON><PERSON>'s context:\n{{paths}}", "runPrompt": "Running prompt in Aider: "}, "power": {"fileRead": "Reading", "fileWrite": {"overwrite": "Rewriting", "append": "Appending to", "createOnly": "Creating"}, "fileEdit": {"title": "Editing", "searchTerm": "Search Term", "regex": "Regex", "replacementText": "Replacement Text", "replaceAll": "Replace All"}, "glob": "Finding files matching pattern: {{pattern}}", "grep": "Searching files matching '{{filePattern}}' for '{{searchTerm}}'", "bash": "Running", "semanticSearch": "Performing semantic search: '{{query}}' in path: '{{path}}'", "agent": {"running": "Running sub-agent...", "completed": "Sub-agent finished.", "prompt": "Prompt"}}, "errors": {"noSuchTool": "Attempted to use unknown tool: {{toolName}}", "invalidToolArguments": "Invalid arguments for tool: {{toolName}}"}}, "codeBlock": {"revertChanges": "Revert changes"}, "diffViewer": {"noChanges": "No changes have been made. Content is identical."}, "promptField": {"placeholders": {"0": "How can I help you today?", "1": "What task can I assist you with?", "2": "What would you like me to code?", "3": "Let me help you solve a problem", "4": "Can I help you with an improvement?", "5": "Wanna refactor some code?", "6": "Can I help you optimize some algorithm?", "7": "Let me help you design a new feature", "8": "Can I help you debug an issue?", "9": "Let me help you create a test suite", "10": "Let me help you implement a design pattern", "11": "Can I explain some code to you?", "12": "Let me help you modernize some legacy code", "13": "Can I help you write documentation?", "14": "Let me help you improve performance", "15": "Give me some task!"}, "questionPlaceholder": "...or suggest something else", "answers": {"yes": "(Y)es", "no": "(N)o", "always": "(A)lways", "dontAsk": "(D)on't ask again"}, "invalidCommand": "Invalid command", "stopResponse": "Stop response", "sendMessage": "Send message (Enter)", "clearChat": "Clear chat"}, "agentProfiles": {"title": "Agent Profiles", "profileSettings": "Agent Profile: {{profileName}}", "newProfileName": "New Profile", "manageProfiles": "Manage Profiles", "profiles": "Profiles", "createNew": "New Profile", "profileName": "Name", "tools": "Tools"}, "projectBar": {"toggleMarkdown": "Markdown rendering", "editFormatTooltip": "This is the format used for editing code. Changing it might cause the model to not perform as expected."}, "reasoningEffort": {"label": "Reasoning", "tooltip": "Reasoning effort determines how much the model will think before taking action. High reasoning effort means the model will think more before taking action, but will also take longer to respond.", "none": "None", "low": "Low", "medium": "Medium", "high": "High", "max": "Max"}, "commands": {"add": "Add files to the context", "agent": "Switch to agent mode. If already in agent mode, opens the agent model selector. Any text after the command is used as a prompt", "architect": "Switch to architect mode. Architect mode is used to plan changes to the code base and execute them", "ask": "Switch to ask mode. Ask questions about the code base without editing any files", "clear": "Clear the chat history", "clear-logs": "Remove only log messages from chat", "code": "Switch to code mode. Ask for changes to your code", "commit": "Commit unstaged changes to the repo", "context": "Switch to context mode. Ask for changes and Aider will add relevant files to the context", "compact": "Summarize the conversation", "copy-context": "Copy the current chat context as markdown, suitable to paste into a web UI", "drop": "Remove files from the context to free up context space", "edit-last": "Edits the last user message", "init": "Initializes PROJECT.md rule file with project information", "agentModeOnly": "Agent mode only", "map": "Print out the current repository map", "map-refresh": "Force a refresh of the repository map", "model": "Open the model selector", "read-only": "Add files to the chat that are for reference only, or turn added files to read-only", "redo": "Redoes the last user prompt.", "reasoning-effort": "Set the reasoning effort level (values: number or low/medium/high depending on model)", "reset": "Drop all files and clear the chat history", "run": "Run a shell command and optionally add the output to the chat", "test": "Run a predefined test command and add the output to the chat on non-zero exit code", "think-tokens": "Set the thinking token budget (supports formats like 8096, 8k, 10.5k, 0.5M)", "tokens": "Report on the number of tokens used by the current chat context", "undo": "Undo the last git commit if it was done by Aider", "web": "Scrape a webpage, and store in the context"}, "telemetry": {"title": "Telemetry Information", "message": "To help us improve AiderDesk, anonymous telemetry data is collected. This includes information like mode usage (agent, code, ask...) and some specifics when Agent mode is used. No personal data or prompts are ever collected.", "fullInfo": "Full information about what is collected can be found <a>here</a>.", "enabledLabel": "Enable anonymous telemetry"}, "multiselect": {"noneSelected": "None selected", "allSelected": "All selected", "someSelected": "{{count}} selected", "selectAll": "Select All"}, "usageDashboard": {"title": "Usage Dashboard", "refresh": "Refresh", "projects": "Projects", "models": "Models", "dateRange": "Date Range", "total": "Total", "thisMonth": "This month", "today": "Today", "all": "All", "tabs": {"table": "Table View", "charts": "Charts View"}, "charts": {"tokenUsageTrend": "Token Usage Trend", "dailyCostBreakdown": "Daily Cost Breakdown", "dailyMessageBreakdown": "Daily Message Breakdown", "modelUsageDistribution": "Model Usage Distribution", "inputTokens": "Input Tokens", "outputTokens": "Output Tokens", "noData": "No data available for the selected period", "tokens": "Tokens", "cost": "Cost", "share": "Share", "moreItems": "+{{count}} more"}, "error": {"fetch": "Failed to fetch usage data."}, "table": {"date": "Date", "project": "Project", "model": "Model", "input": "Input", "output": "Output", "cacheRead": "<PERSON><PERSON>", "cacheWrite": "<PERSON><PERSON>", "totalTokens": "Total Tokens", "cost": "Cost"}}}