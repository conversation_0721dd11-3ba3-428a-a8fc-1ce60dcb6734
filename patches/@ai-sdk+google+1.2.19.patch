diff --git a/node_modules/@ai-sdk/google/dist/index.js b/node_modules/@ai-sdk/google/dist/index.js
index e33247b..a09ddb5 100644
--- a/node_modules/@ai-sdk/google/dist/index.js
+++ b/node_modules/@ai-sdk/google/dist/index.js
@@ -525,7 +525,7 @@ var GoogleGenerativeAILanguageModel = class {
     return this.config.isSupportedUrl(url);
   }
   async doGenerate(options) {
-    var _a, _b, _c, _d, _e;
+    var _a, _b, _c, _d, _e, _f;
     const { args, warnings } = await this.getArgs(options);
     const body = JSON.stringify(args);
     const mergedHeaders = (0, import_provider_utils3.combineHeaders)(
@@ -578,7 +578,8 @@ var GoogleGenerativeAILanguageModel = class {
       providerMetadata: {
         google: {
           groundingMetadata: (_d = candidate.groundingMetadata) != null ? _d : null,
-          safetyRatings: (_e = candidate.safetyRatings) != null ? _e : null
+          safetyRatings: (_e = candidate.safetyRatings) != null ? _e : null,
+          cachedContentTokenCount: (_f = usageMetadata.cachedContentTokenCount) != null ? _f : null,
         }
       },
       sources: extractSources({
@@ -619,7 +620,7 @@ var GoogleGenerativeAILanguageModel = class {
       stream: response.pipeThrough(
         new TransformStream({
           transform(chunk, controller) {
-            var _a, _b, _c, _d, _e, _f;
+            var _a, _b, _c, _d, _e, _f, _g;
             if (!chunk.success) {
               controller.enqueue({ type: "error", error: chunk.error });
               return;
@@ -705,7 +706,8 @@ var GoogleGenerativeAILanguageModel = class {
               providerMetadata = {
                 google: {
                   groundingMetadata: (_e = candidate.groundingMetadata) != null ? _e : null,
-                  safetyRatings: (_f = candidate.safetyRatings) != null ? _f : null
+                  safetyRatings: (_f = candidate.safetyRatings) != null ? _f : null,
+                  cachedContentTokenCount: (_g = usageMetadata.cachedContentTokenCount) != null ? _g : null
                 }
               };
             }
@@ -845,7 +847,8 @@ var responseSchema = import_zod2.z.object({
   usageMetadata: import_zod2.z.object({
     promptTokenCount: import_zod2.z.number().nullish(),
     candidatesTokenCount: import_zod2.z.number().nullish(),
-    totalTokenCount: import_zod2.z.number().nullish()
+    totalTokenCount: import_zod2.z.number().nullish(),
+    cachedContentTokenCount: import_zod2.z.number().nullish()
   }).nullish()
 });
 var chunkSchema = import_zod2.z.object({
@@ -860,7 +863,8 @@ var chunkSchema = import_zod2.z.object({
   usageMetadata: import_zod2.z.object({
     promptTokenCount: import_zod2.z.number().nullish(),
     candidatesTokenCount: import_zod2.z.number().nullish(),
-    totalTokenCount: import_zod2.z.number().nullish()
+    totalTokenCount: import_zod2.z.number().nullish(),
+    cachedContentTokenCount: import_zod2.z.number().nullish(),
   }).nullish()
 });
 var googleGenerativeAIProviderOptionsSchema = import_zod2.z.object({
