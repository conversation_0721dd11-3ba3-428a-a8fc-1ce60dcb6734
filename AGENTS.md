# Agent Guidelines for aider-desk

## Build/Test Commands

- `npm run dev` - Start development server
- `npm run build` - Full build (includes typecheck + electron-vite build + MCP server)
- `npm run lint` - Run ESLint with auto-fix
- `npm run format` - Format code with Prettier
- `npm run typecheck` - Run all TypeScript checks (node + web + mcp)
- `npm run typecheck:web` - Check renderer TypeScript only
- No test framework configured - verify changes manually

## Code Style Guidelines

- Use single quotes, semicolons, trailing commas (Prettier: printWidth 160)
- Arrow functions preferred (`func-style: expression`)
- Import order: builtin → external → internal → parent → sibling → index → type (with newlines between groups)
- React: No `React.FC`, use `type Props` for component props, extract event handlers to separate functions
- Import React types directly: `import { MouseEvent } from 'react'` not `React.MouseEvent`
- Use `clsx` for conditional classes, `react-icons` for icons
- Prefer TypeScript enums over string unions for related constants
- No comments unless complex logic requires explanation
- Use i18n for UI strings (update `src/common/locales/en.json` and `zh.json`)
- Follow existing patterns: check neighboring files and package.json for available libraries
