Scraped content of https://github.com/opencode-ai/opencode:

.turbo-progress-bar { position: fixed; display: block; top: 0; left: 0; height: 3px; background: #0076ff; z-index: 2147483647; transition: width 300ms ease-out, opacity 150ms 150ms ease-in; transform: translate3d(0, 0, 0); }               {"locale":"en","featureFlags":\["alternate\_user\_config\_repo","api\_insights\_show\_missing\_data\_banner","appearance\_settings","attestations\_filtering","attestations\_sorting","codespaces\_prebuild\_region\_target\_update","contact\_requests\_implicit\_opt\_in","contentful\_lp\_copilot\_extensions","contentful\_lp\_flex\_features","contentful\_lp\_footnotes","copilot\_chat\_attach\_multiple\_images","copilot\_chat\_custom\_instructions","copilot\_chat\_repo\_custom\_instructions\_preview","copilot\_chat\_vision\_in\_claude","copilot\_chat\_vision\_skip\_thread\_create","copilot\_chat\_wholearea\_dd","copilot\_custom\_copilots\_feature\_preview","copilot\_duplicate\_thread","copilot\_free\_to\_paid\_telem","copilot\_ftp\_settings\_upgrade","copilot\_ftp\_upgrade\_to\_pro\_from\_models","copilot\_ftp\_your\_copilot\_settings","copilot\_immersive\_agent\_sessions\_direct\_creation","copilot\_immersive\_structured\_model\_picker","copilot\_new\_conversation\_starters","copilot\_new\_immersive\_references\_ui","copilot\_no\_floating\_button","copilot\_paste\_text\_files","copilot\_read\_shared\_conversation","copilot\_showcase\_icebreakers","copilot\_spaces\_support\_forks","copilot\_spark\_single\_user\_iteration","copilot\_spark\_use\_streaming","copilot\_task\_oriented\_assistive\_prompts","copilot\_workbench\_connection\_reload\_banner","copilot\_workbench\_iterate\_panel","copilot\_workbench\_preview\_analytics","copilot\_workbench\_refresh\_on\_wsod","custom\_copilots\_128k\_window","custom\_copilots\_capi\_mode","custom\_copilots\_issues\_prs","direct\_to\_salesforce","dotcom\_chat\_client\_side\_skills","failbot\_report\_error\_react\_apps\_on\_page","ghost\_pilot\_confidence\_truncation\_25","ghost\_pilot\_confidence\_truncation\_40","insert\_before\_patch","issues\_catch\_non\_json\_graphql\_response","issues\_preserve\_tokens\_in\_urls","issues\_react\_blur\_item\_picker\_on\_close","issues\_react\_bots\_timeline\_pagination","issues\_react\_create\_milestone","issues\_react\_prohibit\_title\_fallback","issues\_react\_remove\_placeholders","issues\_tab\_counter\_updates","lifecycle\_label\_name\_updates","link\_contact\_sales\_swp\_marketo","marketing\_pages\_search\_explore\_provider","memex\_mwl\_filter\_field\_delimiter","nonreporting\_relay\_graphql\_status\_codes","primer\_primitives\_experimental","primer\_react\_select\_panel\_with\_modern\_action\_list","remove\_child\_patch","sample\_network\_conn\_type","scheduled\_reminders\_updated\_limits","site\_homepage\_contentful","site\_msbuild\_hide\_integrations","site\_msbuild\_launch","site\_msbuild\_webgl\_hero","spark\_commit\_on\_default\_branch","spark\_sync\_repository\_after\_iteration","swp\_enterprise\_contact\_form","use\_copilot\_avatar","use\_paginated\_repo\_picker\_cost\_center\_form","viewscreen\_sandbox","workbench\_store\_readonly"\]}     GitHub - opencode-ai/opencode: A powerful AI coding agent. Built for the terminal.                                             .w8hcgFksdo30C8w-bygqu{color:#000}.ydkKdaztSS0AeHWIeIHsQ a{color:#0067B8}.erL690\_8JwUW-R4bJRcfl{background-color:#EBEBEB;border:none;color:#000}.erL690\_8JwUW-R4bJRcfl:enabled:hover{color:#000;background-color:#DBDBDB;box-shadow:0px 4px 10px rgba(0,0,0,0.25);border:none}.erL690\_8JwUW-R4bJRcfl:enabled:focus{background-color:#DBDBDB;box-shadow:0px 4px 10px rgba(0,0,0,0.25);border:2px solid #000}.erL690\_8JwUW-R4bJRcfl:disabled{opacity:1;color:rgba(0,0,0,0.2);background-color:rgba(0,0,0,0.2);border:none}.\_1zNQOqxpBFSokeCLGi\_hGr{border:none;background-color:#0067B8;color:#fff}.\_1zNQOqxpBFSokeCLGi\_hGr:enabled:hover{color:#fff;background-color:#0067B8;box-shadow:0px 4px 10px rgba(0,0,0,0.25);border:none}.\_1zNQOqxpBFSokeCLGi\_hGr:enabled:focus{background-color:#0067B8;box-shadow:0px 4px 10px rgba(0,0,0,0.25);border:2px solid #000}.\_1zNQOqxpBFSokeCLGi\_hGr:disabled{opacity:1;color:rgba(0,0,0,0.2);background-color:rgba(0,120,215,0.2);border:none}.\_23tra1HsiiP6cT-Cka-ycB{position:relative;display:flex;z-index:9999;width:100%;background-color:#F2F2F2;justify-content:space-between;text-align:left}div\[dir="rtl"\].\_23tra1HsiiP6cT-Cka-ycB{text-align:right}.\_1Upc2NjY8AlDn177YoVj0y{margin:0;padding-left:5%;padding-top:8px;padding-bottom:8px}div\[dir="rtl"\] .\_1Upc2NjY8AlDn177YoVj0y{margin:0;padding:8px 5% 8px 0;float:none}.\_23tra1HsiiP6cT-Cka-ycB svg{fill:none;max-width:none;max-height:none}.\_1V\_hlU-7jdtPiooHMu89BB{display:table-cell;padding:12px;width:24px;height:24px;font-family:Segoe UI, SegoeUI, Arial, sans-serif;font-style:normal;font-weight:normal;font-size:24px;line-height:0}.f6QKJD7fhSbnJLarTL-W-{display:table-cell;vertical-align:middle;padding:0;font-family:Segoe UI, SegoeUI, Arial, sans-serif;font-style:normal;font-weight:normal;font-size:13px;line-height:16px}.f6QKJD7fhSbnJLarTL-W- a{text-decoration:underline}.\_2j0fmugLb1FgYz6KPuB91w{display:inline-block;margin-left:5%;margin-right:5%;min-width:40%;min-width:calc((150px + 3 \* 4px) \* 2 + 150px);min-width:-webkit-fit-content;min-width:-moz-fit-content;min-width:fit-content;align-self:center;position:relative}.\_1XuCi2WhiqeWRUVp3pnFG3{margin:4px;padding:5px;min-width:150px;min-height:36px;vertical-align:top;cursor:pointer;font-family:Segoe UI, SegoeUI, Arial, sans-serif;font-style:normal;font-weight:normal;font-size:15px;line-height:20px;text-align:center}.\_1XuCi2WhiqeWRUVp3pnFG3:focus{box-sizing:border-box}.\_1XuCi2WhiqeWRUVp3pnFG3:disabled{cursor:not-allowed}.\_2bvsb3ubApyZ0UGoQA9O9T{display:block;position:fixed;z-index:10000;top:0;left:0;width:100%;height:100%;background-color:rgba(255,255,255,0.6);overflow:auto;text-align:left}div\[dir="rtl"\].\_2bvsb3ubApyZ0UGoQA9O9T{text-align:right}div\[dir="rtl"\] .\_2bvsb3ubApyZ0UGoQA9O9T{left:auto;right:0}.AFsJE948muYyzCMktdzuk{position:relative;top:8%;margin-bottom:40px;margin-left:auto;margin-right:auto;box-sizing:border-box;width:640px;background-color:#fff;border:1px solid #0067B8}.\_3kWyBRbW\_dgnMiEyx06Fu4{float:right;z-index:1;margin:2px;padding:12px;border:none;cursor:pointer;font-family:Segoe UI, SegoeUI, Arial, sans-serif;font-style:normal;font-weight:normal;font-size:13px;line-height:13px;display:flex;align-items:center;text-align:center;color:#666;background-color:#fff}div\[dir="rtl"\] .\_3kWyBRbW\_dgnMiEyx06Fu4{margin:2px;padding:12px;float:left}.uCYvKvHXrhjNgflv1VqdD{position:static;margin-top:36px;margin-left:36px;margin-right:36px}.\_17pX1m9O\_W--iZbDt3Ta5r{margin-top:0;margin-bottom:12px;font-family:Segoe UI, SegoeUI, Arial, sans-serif;font-style:normal;font-weight:600;font-size:20px;line-height:24px;text-transform:none}.\_1kBkHQ1V1wu3kl-YcLgUr6{height:446px;overflow:auto}.\_20\_nXDf6uFs9Q6wxRXG-I-{margin-top:0;font-family:Segoe UI, SegoeUI, Arial, sans-serif;font-style:normal;font-weight:normal;font-size:15px;line-height:20px}.\_20\_nXDf6uFs9Q6wxRXG-I- a{text-decoration:underline}dl.\_2a0NH\_GDQEQe5Ynfo7suVH{margin-top:36px;margin-bottom:0;padding:0;list-style:none;text-transform:none}dt.\_3j\_LCPv7fyXv3A8FIXVwZ4{margin-top:20px;float:none;font-family:Segoe UI, SegoeUI, Arial, sans-serif;font-style:normal;font-weight:600;font-size:18px;line-height:24px;list-style:none}.k-vxTGFbdq1aOZB2HHpjh{margin:0;padding:0;border:none}.\_2Bucyy75c\_ogoU1g-liB5R{margin:0;padding:0;border-bottom:none;font-family:Segoe UI, SegoeUI, Arial, sans-serif;font-style:normal;font-weight:600;font-size:18px;line-height:24px;text-transform:none}.\_63gwfzV8dclrsl2cfd90r{display:inline-block;margin-top:0;margin-bottom:13px;font-family:Segoe UI, SegoeUI, Arial, sans-serif;font-style:normal;font-weight:normal;font-size:15px;line-height:20px}.\_1l8wM\_4mRYGz3Iu7l3BZR7{display:block}.\_2UE03QS02aZGkslegN\_F-i{display:inline-block;position:relative;left:5px;margin-bottom:13px;margin-right:34px;padding:3px}div\[dir="rtl"\] .\_2UE03QS02aZGkslegN\_F-i{margin:0 0 13px 34px;padding:3px;float:none}div\[dir="rtl"\] .\_2UE03QS02aZGkslegN\_F-i{left:auto;right:5px}.\_23tra1HsiiP6cT-Cka-ycB \*::before,.\_2bvsb3ubApyZ0UGoQA9O9T \*::before,.\_23tra1HsiiP6cT-Cka-ycB \*::after,.\_2bvsb3ubApyZ0UGoQA9O9T \*::after{box-sizing:inherit}.\_1HSFn0HzGo6w4ADApV8-c4{outline:2px solid rgba(0,0,0,0.8)}input\[type="radio"\].\_1dp8Vp5m3HwAqGx8qBmFV2{display:inline-block;position:relative;margin-top:0;margin-left:0;margin-right:0;height:0;width:0;border-radius:0;cursor:pointer;outline:none;box-sizing:border-box;-webkit-appearance:none;-moz-appearance:none;appearance:none}input\[type="radio"\].\_1dp8Vp5m3HwAqGx8qBmFV2+label::before{display:block;position:absolute;top:5px;left:3px;height:19px;width:19px;content:"";border-radius:50%;border:1px solid #000;background-color:#fff}div\[dir="rtl"\] input\[type="radio"\].\_1dp8Vp5m3HwAqGx8qBmFV2+label::before{left:auto;right:3px}input\[type="radio"\].\_1dp8Vp5m3HwAqGx8qBmFV2:not(:disabled)+label:hover::before{border:1px solid #0067B8}input\[type="radio"\].\_1dp8Vp5m3HwAqGx8qBmFV2:not(:disabled)+label:hover::after{display:block;position:absolute;top:10px;left:8px;height:9px;width:9px;content:"";border-radius:50%;background-color:rgba(0,0,0,0.8)}div\[dir="rtl"\] input\[type="radio"\].\_1dp8Vp5m3HwAqGx8qBmFV2:not(:disabled)+label:hover::after{left:auto;right:8px}input\[type="radio"\].\_1dp8Vp5m3HwAqGx8qBmFV2:not(:disabled)+label:focus::before{border:1px solid #0067B8}input\[type="radio"\].\_1dp8Vp5m3HwAqGx8qBmFV2:not(:disabled)+label:focus::after{display:block;position:absolute;top:10px;left:8px;height:9px;width:9px;content:"";border-radius:50%;background-color:#000}div\[dir="rtl"\] input\[type="radio"\].\_1dp8Vp5m3HwAqGx8qBmFV2:not(:disabled)+label:focus::after{left:auto;right:8px}input\[type="radio"\].\_1dp8Vp5m3HwAqGx8qBmFV2:checked+label::after{display:block;position:absolute;top:10px;left:8px;height:9px;width:9px;content:"";border-radius:50%;background-color:#000}div\[dir="rtl"\] input\[type="radio"\].\_1dp8Vp5m3HwAqGx8qBmFV2:checked+label::after{left:auto;right:8px}input\[type="radio"\].\_1dp8Vp5m3HwAqGx8qBmFV2:disabled+label{cursor:not-allowed}input\[type="radio"\].\_1dp8Vp5m3HwAqGx8qBmFV2:disabled+label::before{border:1px solid rgba(0,0,0,0.2);background-color:rgba(0,0,0,0.2)}.\_3RJzeL3l9Rl\_lAQEm6VwdX{display:block;position:static;float:right;margin-top:0;margin-bottom:0;margin-left:19px;margin-right:0;padding-top:0;padding-bottom:0;padding-left:8px;padding-right:0;width:80%;width:calc(100% - 19px);font-family:Segoe UI, SegoeUI, Arial, sans-serif;font-style:normal;font-weight:normal;font-size:15px;line-height:20px;text-transform:none;cursor:pointer;box-sizing:border-box}div\[dir="rtl"\] .\_3RJzeL3l9Rl\_lAQEm6VwdX{margin:0 19px 0 0;padding:0 8px 0 0;float:left}.nohp3sIG12ZBhzcMnPala{margin-top:20px;margin-bottom:48px}.\_2uhaEsmeotZ3P-M0AXo2kF{padding:0;width:278px;height:36px;cursor:pointer;font-family:Segoe UI, SegoeUI, Arial, sans-serif;font-style:normal;font-weight:normal;font-size:15px;line-height:20px;text-align:center}.\_2uhaEsmeotZ3P-M0AXo2kF:focus{box-sizing:border-box}.\_2uhaEsmeotZ3P-M0AXo2kF:disabled{cursor:not-allowed}.\_3tOu1FJ59c\_xz\_PmI1lKV5{float:right;padding:0;width:278px;height:36px;cursor:pointer;font-family:Segoe UI, SegoeUI, Arial, sans-serif;font-style:normal;font-weight:normal;font-size:15px;line-height:20px;text-align:center}.\_3tOu1FJ59c\_xz\_PmI1lKV5:focus{box-sizing:border-box}.\_3tOu1FJ59c\_xz\_PmI1lKV5:disabled{cursor:not-allowed}div\[dir="rtl"\] .\_3tOu1FJ59c\_xz\_PmI1lKV5{margin:0;padding:0;float:left}@media only screen and (max-width: 768px){.\_2j0fmugLb1FgYz6KPuB91w,.\_1Upc2NjY8AlDn177YoVj0y{padding-top:8px;padding-bottom:12px;padding-left:3.75%;padding-right:3.75%;margin:0;width:92.5%}.\_23tra1HsiiP6cT-Cka-ycB{display:block}.\_1XuCi2WhiqeWRUVp3pnFG3{margin-bottom:8px;margin-left:0;margin-right:0;width:100%}.\_2bvsb3ubApyZ0UGoQA9O9T{overflow:hidden}.AFsJE948muYyzCMktdzuk{top:1.8%;width:93.33%;height:96.4%;overflow:hidden}.uCYvKvHXrhjNgflv1VqdD{margin-top:24px;margin-left:24px;margin-right:24px;height:100%}.\_1kBkHQ1V1wu3kl-YcLgUr6{height:62%;height:calc(100% - 188px);min-height:50%}.\_2uhaEsmeotZ3P-M0AXo2kF{width:100%}.\_3tOu1FJ59c\_xz\_PmI1lKV5{margin-bottom:12px;margin-left:0;width:100%}div\[dir="rtl"\] .\_3tOu1FJ59c\_xz\_PmI1lKV5{margin:0 0 12px 0;padding:0;float:none}}@media only screen and (max-width: 768px) and (orientation: landscape), only screen and (max-height: 260px), only screen and (max-width: 340px){.AFsJE948muYyzCMktdzuk{overflow:auto}}@media only screen and (max-height: 260px), only screen and (max-width: 340px){.\_1XuCi2WhiqeWRUVp3pnFG3{min-width:0}.\_3kWyBRbW\_dgnMiEyx06Fu4{padding:3%}.uCYvKvHXrhjNgflv1VqdD{margin-top:3%;margin-left:3%;margin-right:3%}.\_17pX1m9O\_W--iZbDt3Ta5r{margin-bottom:3%}.\_1kBkHQ1V1wu3kl-YcLgUr6{height:calc(79% - 64px)}.nohp3sIG12ZBhzcMnPala{margin-top:5%;margin-bottom:10%}.\_3tOu1FJ59c\_xz\_PmI1lKV5{margin-bottom:3%}div\[dir="rtl"\] .\_3tOu1FJ59c\_xz\_PmI1lKV5{margin:0 0 3% 0;padding:0;float:none}} .\_23tra1HsiiP6cT-Cka-ycB { background-color: #24292f !important; }.w8hcgFksdo30C8w-bygqu { color: #ffffff !important; }.ydkKdaztSS0AeHWIeIHsQ a { color: #d8b9ff !important; }.\_2bvsb3ubApyZ0UGoQA9O9T { background-color: rgba(23, 23, 23, 0.8) !important; }.AFsJE948muYyzCMktdzuk { background-color: #24292f !important; border: 1px solid #d8b9ff !important; }.\_3kWyBRbW\_dgnMiEyx06Fu4 { color: #d8b9ff !important; background-color: #24292f !important; }.\_1zNQOqxpBFSokeCLGi\_hGr { border: 1px solid #ffffff !important; background-color: #ffffff !important; color: #1f2328 !important; }.\_1zNQOqxpBFSokeCLGi\_hGr:enabled:hover { color: #1f2328 !important; background-color: #d8b9ff !important; box-shadow: none !important; border: 1px solid transparent !important; }.\_1zNQOqxpBFSokeCLGi\_hGr:enabled:focus { background-color: #d8b9ff !important; box-shadow: none !important; border: 2px solid #ffffff !important; }.\_1zNQOqxpBFSokeCLGi\_hGr:disabled { opacity: 0.5 !important; color: #1f2328 !important; background-color: #ffffff !important; border: 1px solid transparent !important; }.erL690\_8JwUW-R4bJRcfl { border: 1px solid #eaeef2 !important; background-color: #32383f !important; color: #ffffff !important; }.erL690\_8JwUW-R4bJRcfl:enabled:hover { color: #ffffff !important; background-color: #24292f !important; box-shadow: none !important; border: 1px solid #ffffff !important; }.erL690\_8JwUW-R4bJRcfl:enabled:focus { background-color: #24292f !important; box-shadow: none !important; border: 2px solid #6e7781 !important; }.erL690\_8JwUW-R4bJRcfl:disabled { opacity: 0.5 !important; color: #ffffff !important; background-color: #424a53 !important; border: 1px solid #6e7781 !important; }input\[type="radio"\].\_1dp8Vp5m3HwAqGx8qBmFV2 + label::before { border: 1px solid #d8b9ff !important; background-color: #24292f !important; }.\_1HSFn0HzGo6w4ADApV8-c4 { outline: 2px solid #ffffff !important; }input\[type="radio"\].\_1dp8Vp5m3HwAqGx8qBmFV2:checked + label::after { background-color: #d8b9ff !important; }input\[type="radio"\].\_1dp8Vp5m3HwAqGx8qBmFV2 + label:hover::before { border: 1px solid #ffffff !important; }input\[type="radio"\].\_1dp8Vp5m3HwAqGx8qBmFV2 + label:hover::after { background-color: #ffffff !important; }input\[type="radio"\].\_1dp8Vp5m3HwAqGx8qBmFV2 + label:focus::before { border: 1px solid #ffffff !important; }input\[type="radio"\].\_1dp8Vp5m3HwAqGx8qBmFV2 + label:focus::after { background-color: #d8b9ff !important; }input\[type="radio"\].\_1dp8Vp5m3HwAqGx8qBmFV2:disabled + label::before { border: 1px solid rgba(227, 227, 227, 0.2) !important; background-color: rgba(227, 227, 227, 0.2) !important; }

[Skip to content](#start-of-content)  {"props":{"docsUrl":"https://docs.github.com/get-started/accessibility/keyboard-shortcuts"}}

{"resolvedServerColorMode":"day"}

Navigation Menu
---------------

Toggle navigation

[](/)

[Sign in](/login?return_to=https%3A%2F%2Fgithub.com%2Fopencode-ai%2Fopencode)

Appearance settings  {"props":{}}

{"resolvedServerColorMode":"day"}

*   Product
    
    *   [
        
        GitHub Copilot
        
        Write better code with AI
        
        ](https://github.com/features/copilot)
    *   [
        
        GitHub Models New
        
        Manage and compare prompts
        
        ](https://github.com/features/models)
    *   [
        
        GitHub Advanced Security
        
        Find and fix vulnerabilities
        
        ](https://github.com/security/advanced-security)
    *   [
        
        Actions
        
        Automate any workflow
        
        ](https://github.com/features/actions)
    *   [
        
        Codespaces
        
        Instant dev environments
        
        ](https://github.com/features/codespaces)
    
    *   [
        
        Issues
        
        Plan and track work
        
        ](https://github.com/features/issues)
    *   [
        
        Code Review
        
        Manage code changes
        
        ](https://github.com/features/code-review)
    *   [
        
        Discussions
        
        Collaborate outside of code
        
        ](https://github.com/features/discussions)
    *   [
        
        Code Search
        
        Find more, search less
        
        ](https://github.com/features/code-search)
    
    Explore
    
    *   [Why GitHub](https://github.com/why-github)
    *   [All features](https://github.com/features)
    *   [Documentation](https://docs.github.com)
    *   [GitHub Skills](https://skills.github.com)
    *   [Blog](https://github.blog)
    
*   Solutions
    
    By company size
    
    *   [Enterprises](https://github.com/enterprise)
    *   [Small and medium teams](https://github.com/team)
    *   [Startups](https://github.com/enterprise/startups)
    *   [Nonprofits](/solutions/industry/nonprofits)
    
    By use case
    
    *   [DevSecOps](/solutions/use-case/devsecops)
    *   [DevOps](/solutions/use-case/devops)
    *   [CI/CD](/solutions/use-case/ci-cd)
    *   [View all use cases](/solutions/use-case)
    
    By industry
    
    *   [Healthcare](/solutions/industry/healthcare)
    *   [Financial services](/solutions/industry/financial-services)
    *   [Manufacturing](/solutions/industry/manufacturing)
    *   [Government](/solutions/industry/government)
    *   [View all industries](/solutions/industry)
    
    [View all solutions](/solutions)
    
*   Resources
    
    Topics
    
    *   [AI](/resources/articles/ai)
    *   [DevOps](/resources/articles/devops)
    *   [Security](/resources/articles/security)
    *   [Software Development](/resources/articles/software-development)
    *   [View all](/resources/articles)
    
    Explore
    
    *   [Learning Pathways](https://resources.github.com/learn/pathways)
    *   [Events & Webinars](https://resources.github.com)
    *   [Ebooks & Whitepapers](https://github.com/resources/whitepapers)
    *   [Customer Stories](https://github.com/customer-stories)
    *   [Partners](https://partner.github.com)
    *   [Executive Insights](https://github.com/solutions/executive-insights)
    
*   Open Source
    
    *   [
        
        GitHub Sponsors
        
        Fund open source developers
        
        ](/sponsors)
    
    *   [
        
        The ReadME Project
        
        GitHub community articles
        
        ](https://github.com/readme)
    
    Repositories
    
    *   [Topics](https://github.com/topics)
    *   [Trending](https://github.com/trending)
    *   [Collections](https://github.com/collections)
    
*   Enterprise
    
    *   [
        
        Enterprise platform
        
        AI-powered developer platform
        
        ](/enterprise)
    
    Available add-ons
    
    *   [
        
        GitHub Advanced Security
        
        Enterprise-grade security features
        
        ](https://github.com/security/advanced-security)
    *   [
        
        Copilot for business
        
        Enterprise-grade AI features
        
        ](/features/copilot/copilot-business)
    *   [
        
        Premium Support
        
        Enterprise-grade 24/7 support
        
        ](/premium-support)
    
*   [Pricing](https://github.com/pricing)

Search or jump to...

Search code, repositories, users, issues, pull requests...
==========================================================

Search

Clear

[Search syntax tips](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax)

Provide feedback
================

We read every piece of feedback, and take your input very seriously.

 Include my email address so I can be contacted

Cancel Submit feedback

Saved searches
==============

Use saved searches to filter your results more quickly
------------------------------------------------------

Name  

Query 

To see all available qualifiers, see our [documentation](https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax).

Cancel Create saved search

[Sign in](/login?return_to=https%3A%2F%2Fgithub.com%2Fopencode-ai%2Fopencode)

[Sign up](/signup?ref_cta=Sign+up&ref_loc=header+logged+out&ref_page=%2F%3Cuser-name%3E%2F%3Crepo-name%3E&source=header-repo&source_repo=opencode-ai%2Fopencode)

Appearance settings  {"props":{}}

{"resolvedServerColorMode":"day"}

Resetting focus

You signed in with another tab or window. Reload to refresh your session. You signed out in another tab or window. Reload to refresh your session. You switched accounts on another tab or window. Reload to refresh your session. Dismiss alert

[opencode-ai](/opencode-ai) / **[opencode](/opencode-ai/opencode)** Public

*   [Notifications](/login?return_to=%2Fopencode-ai%2Fopencode) You must be signed in to change notification settings
*   [Fork 499](/login?return_to=%2Fopencode-ai%2Fopencode)
*   [Star 6.7k](/login?return_to=%2Fopencode-ai%2Fopencode)
    

A powerful AI coding agent. Built for the terminal.

### License

[MIT license](/opencode-ai/opencode/blob/main/LICENSE)

[6.7k stars](/opencode-ai/opencode/stargazers) [499 forks](/opencode-ai/opencode/forks) [Branches](/opencode-ai/opencode/branches) [Tags](/opencode-ai/opencode/tags) [Activity](/opencode-ai/opencode/activity)

[Star](/login?return_to=%2Fopencode-ai%2Fopencode)

[Notifications](/login?return_to=%2Fopencode-ai%2Fopencode) You must be signed in to change notification settings

*   [Code](/opencode-ai/opencode)
*   [Issues 88](/opencode-ai/opencode/issues)
*   [Pull requests 31](/opencode-ai/opencode/pulls)
*   [Discussions](/opencode-ai/opencode/discussions)
*   [Actions](/opencode-ai/opencode/actions)
*   [Projects 0](/opencode-ai/opencode/projects)
*   [Security](/opencode-ai/opencode/security)
    
    [](/opencode-ai/opencode/security)
    
    [](/opencode-ai/opencode/security)
    
    [](/opencode-ai/opencode/security)
    
    [
    
    ### Uh oh!
    
    ](/opencode-ai/opencode/security)
    
    [There was an error while loading.](/opencode-ai/opencode/security) Please reload this page.
    
*   [Insights](/opencode-ai/opencode/pulse)

Additional navigation options

*   [Code](/opencode-ai/opencode)
*   [Issues](/opencode-ai/opencode/issues)
*   [Pull requests](/opencode-ai/opencode/pulls)
*   [Discussions](/opencode-ai/opencode/discussions)
*   [Actions](/opencode-ai/opencode/actions)
*   [Projects](/opencode-ai/opencode/projects)
*   [Security](/opencode-ai/opencode/security)
*   [Insights](/opencode-ai/opencode/pulse)

opencode-ai/opencode
====================

 {"props":{"initialPayload":{"allShortcutsEnabled":false,"path":"/","repo":{"id":949662713,"defaultBranch":"main","name":"opencode","ownerLogin":"opencode-ai","currentUserCanPush":false,"isFork":false,"isEmpty":false,"createdAt":"2025-03-16T23:38:55.000Z","ownerAvatar":"https://avatars.githubusercontent.com/u/208539476?v=4","public":true,"private":false,"isOrgOwned":true},"currentUser":null,"refInfo":{"name":"main","listCacheKey":"v0:1751007061.0","canEdit":false,"refType":"branch","currentOid":"f0571f5f5adef12eba9ddf6d07223a043d63dca8"},"tree":{"items":\[{"name":".github/workflows","path":".github/workflows","contentType":"directory","hasSimplifiedPath":true},{"name":"cmd","path":"cmd","contentType":"directory"},{"name":"internal","path":"internal","contentType":"directory"},{"name":"scripts","path":"scripts","contentType":"directory"},{"name":".gitignore","path":".gitignore","contentType":"file"},{"name":".goreleaser.yml","path":".goreleaser.yml","contentType":"file"},{"name":".opencode.json","path":".opencode.json","contentType":"file"},{"name":"LICENSE","path":"LICENSE","contentType":"file"},{"name":"README.md","path":"README.md","contentType":"file"},{"name":"go.mod","path":"go.mod","contentType":"file"},{"name":"go.sum","path":"go.sum","contentType":"file"},{"name":"install","path":"install","contentType":"file"},{"name":"main.go","path":"main.go","contentType":"file"},{"name":"opencode-schema.json","path":"opencode-schema.json","contentType":"file"},{"name":"sqlc.yaml","path":"sqlc.yaml","contentType":"file"}\],"templateDirectorySuggestionUrl":null,"readme":null,"totalCount":15,"showBranchInfobar":false},"fileTree":null,"fileTreeProcessingTime":null,"foldersToFetch":\[\],"treeExpanded":false,"symbolsExpanded":false,"isOverview":true,"overview":{"banners":{"shouldRecommendReadme":false,"isPersonalRepo":false,"showUseActionBanner":false,"actionSlug":null,"actionId":null,"showProtectBranchBanner":false,"publishBannersInfo":{"dismissActionNoticePath":"/settings/dismiss-notice/publish\_action\_from\_repo","releasePath":"/opencode-ai/opencode/releases/new?marketplace=true","showPublishActionBanner":false},"interactionLimitBanner":null,"showInvitationBanner":false,"inviterName":null,"actionsMigrationBannerInfo":{"releaseTags":\[\],"showImmutableActionsMigrationBanner":false,"initialMigrationStatus":null},"showDeployBanner":false,"detectedStack":{"framework":null,"packageManager":null}},"codeButton":{"contactPath":"/contact","isEnterprise":false,"local":{"protocolInfo":{"httpAvailable":true,"sshAvailable":null,"httpUrl":"https://github.com/opencode-ai/opencode.git","showCloneWarning":null,"sshUrl":null,"sshCertificatesRequired":null,"sshCertificatesAvailable":null,"ghCliUrl":"gh repo clone opencode-ai/opencode","defaultProtocol":"http","newSshKeyUrl":"/settings/ssh/new","setProtocolPath":"/users/set\_protocol"},"platformInfo":{"cloneUrl":"https://desktop.github.com","showVisualStudioCloneButton":false,"visualStudioCloneUrl":"https://windows.github.com","showXcodeCloneButton":false,"xcodeCloneUrl":"xcode://clone?repo=https%3A%2F%2Fgithub.com%2Fopencode-ai%2Fopencode","zipballUrl":"/opencode-ai/opencode/archive/refs/heads/main.zip"}},"newCodespacePath":"/codespaces/new?hide\_repo\_select=true\\u0026repo=949662713"},"popovers":{"rename":null,"renamedParentRepo":null},"commitCount":"183","overviewFiles":\[{"displayName":"README.md","repoName":"opencode","refName":"main","path":"README.md","preferredFileType":"readme","tabName":"README","richText":"\\u003carticle class=\\"markdown-body entry-content container-lg\\" itemprop=\\"text\\"\\u003e\\u003cdiv class=\\"markdown-alert markdown-alert-note\\" dir=\\"auto\\"\\u003e\\u003cp class=\\"markdown-alert-title\\" dir=\\"auto\\"\\u003e\\u003csvg class=\\"octicon octicon-info mr-2\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8Zm8-6.5a6.5 6.5 0 1 0 0 13 6.5 6.5 0 0 0 0-13ZM6.5 7.75A.75.75 0 0 1 7.25 7h1a.75.75 0 0 1 .75.75v2.75h.25a.75.75 0 0 1 0 1.5h-2a.75.75 0 0 1 0-1.5h.25v-2h-.25a.75.75 0 0 1-.75-.75ZM8 6a1 1 0 1 1 0-2 1 1 0 0 1 0 2Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003eNote\\u003c/p\\u003e\\u003cp dir=\\"auto\\"\\u003eThis is the original OpenCode repository, now continuing at \\u003ca href=\\"https://github.com/charmbracelet\\"\\u003eCharm\\u003c/a\\u003e with its original creator, \\u003ca href=\\"https://github.com/kujtimiihoxha\\"\\u003eKujtim Hoxha\\u003c/a\\u003e.\\u003cbr\\u003e\\nDevelopment is continuing under a new name as we prepare for a public relaunch.\\u003cbr\\u003e\\nFollow \\u003ca href=\\"https://x.com/charmcli\\" rel=\\"nofollow\\"\\u003e@charmcli\\u003c/a\\u003e or join our \\u003ca href=\\"https://charm.sh/chat\\" rel=\\"nofollow\\"\\u003eDiscord\\u003c/a\\u003e for updates.\\u003c/p\\u003e\\n\\u003c/div\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch1 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003e⌬ OpenCode\\u003c/h1\\u003e\\u003ca id=\\"user-content--opencode\\" class=\\"anchor\\" aria-label=\\"Permalink: ⌬ OpenCode\\" href=\\"#-opencode\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cp align=\\"center\\" dir=\\"auto\\"\\u003e\\u003ca target=\\"\_blank\\" rel=\\"noopener noreferrer\\" href=\\"https://private-user-images.githubusercontent.com/25087/444283386-9ae61ef6-70e5-4876-bc45-5bcb4e52c714.gif?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NTE2MjQ4ODUsIm5iZiI6MTc1MTYyNDU4NSwicGF0aCI6Ii8yNTA4Ny80NDQyODMzODYtOWFlNjFlZjYtNzBlNS00ODc2LWJjNDUtNWJjYjRlNTJjNzE0LmdpZj9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNTA3MDQlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjUwNzA0VDEwMjMwNVomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPWE0MDE0MzgxNDBhNDQ0YjAzYTgwYjE2ZmUyNGI4YzQ4NDhhNGNmMzU0MDk1ZTcxMGU2MjA3OWM1N2M3ODA5NDYmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0In0.3PpijXrOsAuql2MI0VG40QcnDDe5WGFBlGs3euM5yTU\\"\\u003e\\u003cimg src=\\"https://private-user-images.githubusercontent.com/25087/444283386-9ae61ef6-70e5-4876-bc45-5bcb4e52c714.gif?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJnaXRodWIuY29tIiwiYXVkIjoicmF3LmdpdGh1YnVzZXJjb250ZW50LmNvbSIsImtleSI6ImtleTUiLCJleHAiOjE3NTE2MjQ4ODUsIm5iZiI6MTc1MTYyNDU4NSwicGF0aCI6Ii8yNTA4Ny80NDQyODMzODYtOWFlNjFlZjYtNzBlNS00ODc2LWJjNDUtNWJjYjRlNTJjNzE0LmdpZj9YLUFtei1BbGdvcml0aG09QVdTNC1ITUFDLVNIQTI1NiZYLUFtei1DcmVkZW50aWFsPUFLSUFWQ09EWUxTQTUzUFFLNFpBJTJGMjAyNTA3MDQlMkZ1cy1lYXN0LTElMkZzMyUyRmF3czRfcmVxdWVzdCZYLUFtei1EYXRlPTIwMjUwNzA0VDEwMjMwNVomWC1BbXotRXhwaXJlcz0zMDAmWC1BbXotU2lnbmF0dXJlPWE0MDE0MzgxNDBhNDQ0YjAzYTgwYjE2ZmUyNGI4YzQ4NDhhNGNmMzU0MDk1ZTcxMGU2MjA3OWM1N2M3ODA5NDYmWC1BbXotU2lnbmVkSGVhZGVycz1ob3N0In0.3PpijXrOsAuql2MI0VG40QcnDDe5WGFBlGs3euM5yTU\\" width=\\"800\\" data-animated-image=\\"\\" style=\\"max-width: 100%;\\"\\u003e\\u003c/a\\u003e\\u003c/p\\u003e\\n\\u003cblockquote\\u003e\\n\\u003cp dir=\\"auto\\"\\u003e\\u003cstrong\\u003e\\u003cg-emoji class=\\"g-emoji\\" alias=\\"warning\\"\\u003e⚠️\\u003c/g-emoji\\u003e Early Development Notice:\\u003c/strong\\u003e This project is in early development and is not yet ready for production use. Features may change, break, or be incomplete. Use at your own risk.\\u003c/p\\u003e\\n\\u003c/blockquote\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eA powerful terminal-based AI assistant for developers, providing intelligent coding assistance directly in your terminal.\\u003c/p\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch2 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eOverview\\u003c/h2\\u003e\\u003ca id=\\"user-content-overview\\" class=\\"anchor\\" aria-label=\\"Permalink: Overview\\" href=\\"#overview\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eOpenCode is a Go-based CLI application that brings AI assistance to your terminal. It provides a TUI (Terminal User Interface) for interacting with various AI models to help with coding tasks, debugging, and more.\\u003c/p\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eFor a quick video overview, check out\\n\\u003ca href=\\"https://www.youtube.com/watch?v=P8luPmEa1QI\\" rel=\\"nofollow\\"\\u003e\\u003cimg width=\\"25\\" src=\\"https://camo.githubusercontent.com/3744c496d91bb014d72b7ef2dc4c827f2c1d41ec9c01cb8883be3cdb5ca486ce/68747470733a2f2f75706c6f61642e77696b696d656469612e6f72672f77696b6970656469612f636f6d6d6f6e732f302f30392f596f75547562655f66756c6c2d636f6c6f725f69636f6e5f253238323031372532392e737667\\" data-canonical-src=\\"https://upload.wikimedia.org/wikipedia/commons/0/09/YouTube\_full-color\_icon\_%282017%29.svg\\" style=\\"max-width: 100%;\\"\\u003e OpenCode + Gemini 2.5 Pro: BYE Claude Code! I'm SWITCHING To the FASTEST AI Coder!\\u003c/a\\u003e\\u003c/p\\u003e\\n\\u003cp dir=\\"auto\\"\\u003e\\u003ca href=\\"https://www.youtube.com/watch?v=P8luPmEa1QI\\" rel=\\"nofollow\\"\\u003e\\u003cimg width=\\"550\\" src=\\"https://camo.githubusercontent.com/02859f4e0c37f4f66f06a62ffe41a98cfceead2387dd083cbb52c574942696b5/68747470733a2f2f69332e7974696d672e636f6d2f76692f50386c75506d45613151492f6d617872657364656661756c742e6a7067\\" data-canonical-src=\\"https://i3.ytimg.com/vi/P8luPmEa1QI/maxresdefault.jpg\\" style=\\"max-width: 100%;\\"\\u003e\\u003c/a\\u003e\\u003c/p\\u003e\\u003cp dir=\\"auto\\"\\u003e\\u003c/p\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch2 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eFeatures\\u003c/h2\\u003e\\u003ca id=\\"user-content-features\\" class=\\"anchor\\" aria-label=\\"Permalink: Features\\" href=\\"#features\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cul dir=\\"auto\\"\\u003e\\n\\u003cli\\u003e\\u003cstrong\\u003eInteractive TUI\\u003c/strong\\u003e: Built with \\u003ca href=\\"https://github.com/charmbracelet/bubbletea\\"\\u003eBubble Tea\\u003c/a\\u003e for a smooth terminal experience\\u003c/li\\u003e\\n\\u003cli\\u003e\\u003cstrong\\u003eMultiple AI Providers\\u003c/strong\\u003e: Support for OpenAI, Anthropic Claude, Google Gemini, AWS Bedrock, Groq, Azure OpenAI, and OpenRouter\\u003c/li\\u003e\\n\\u003cli\\u003e\\u003cstrong\\u003eSession Management\\u003c/strong\\u003e: Save and manage multiple conversation sessions\\u003c/li\\u003e\\n\\u003cli\\u003e\\u003cstrong\\u003eTool Integration\\u003c/strong\\u003e: AI can execute commands, search files, and modify code\\u003c/li\\u003e\\n\\u003cli\\u003e\\u003cstrong\\u003eVim-like Editor\\u003c/strong\\u003e: Integrated editor with text input capabilities\\u003c/li\\u003e\\n\\u003cli\\u003e\\u003cstrong\\u003ePersistent Storage\\u003c/strong\\u003e: SQLite database for storing conversations and sessions\\u003c/li\\u003e\\n\\u003cli\\u003e\\u003cstrong\\u003eLSP Integration\\u003c/strong\\u003e: Language Server Protocol support for code intelligence\\u003c/li\\u003e\\n\\u003cli\\u003e\\u003cstrong\\u003eFile Change Tracking\\u003c/strong\\u003e: Track and visualize file changes during sessions\\u003c/li\\u003e\\n\\u003cli\\u003e\\u003cstrong\\u003eExternal Editor Support\\u003c/strong\\u003e: Open your preferred editor for composing messages\\u003c/li\\u003e\\n\\u003cli\\u003e\\u003cstrong\\u003eNamed Arguments for Custom Commands\\u003c/strong\\u003e: Create powerful custom commands with multiple named placeholders\\u003c/li\\u003e\\n\\u003c/ul\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch2 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eInstallation\\u003c/h2\\u003e\\u003ca id=\\"user-content-installation\\" class=\\"anchor\\" aria-label=\\"Permalink: Installation\\" href=\\"#installation\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eUsing the Install Script\\u003c/h3\\u003e\\u003ca id=\\"user-content-using-the-install-script\\" class=\\"anchor\\" aria-label=\\"Permalink: Using the Install Script\\" href=\\"#using-the-install-script\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cdiv class=\\"highlight highlight-source-shell notranslate position-relative overflow-auto\\" dir=\\"auto\\" data-snippet-clipboard-copy-content=\\"# Install the latest version\\ncurl -fsSL https://raw.githubusercontent.com/opencode-ai/opencode/refs/heads/main/install | bash\\n\\n# Install a specific version\\ncurl -fsSL https://raw.githubusercontent.com/opencode-ai/opencode/refs/heads/main/install | VERSION=0.1.0 bash\\"\\u003e\\u003cpre\\u003e\\u003cspan class=\\"pl-c\\"\\u003e\\u003cspan class=\\"pl-c\\"\\u003e#\\u003c/span\\u003e Install the latest version\\u003c/span\\u003e\\ncurl -fsSL https://raw.githubusercontent.com/opencode-ai/opencode/refs/heads/main/install \\u003cspan class=\\"pl-k\\"\\u003e|\\u003c/span\\u003e bash\\n\\n\\u003cspan class=\\"pl-c\\"\\u003e\\u003cspan class=\\"pl-c\\"\\u003e#\\u003c/span\\u003e Install a specific version\\u003c/span\\u003e\\ncurl -fsSL https://raw.githubusercontent.com/opencode-ai/opencode/refs/heads/main/install \\u003cspan class=\\"pl-k\\"\\u003e|\\u003c/span\\u003e VERSION=0.1.0 bash\\u003c/pre\\u003e\\u003c/div\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eUsing Homebrew (macOS and Linux)\\u003c/h3\\u003e\\u003ca id=\\"user-content-using-homebrew-macos-and-linux\\" class=\\"anchor\\" aria-label=\\"Permalink: Using Homebrew (macOS and Linux)\\" href=\\"#using-homebrew-macos-and-linux\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cdiv class=\\"highlight highlight-source-shell notranslate position-relative overflow-auto\\" dir=\\"auto\\" data-snippet-clipboard-copy-content=\\"brew install opencode-ai/tap/opencode\\"\\u003e\\u003cpre\\u003ebrew install opencode-ai/tap/opencode\\u003c/pre\\u003e\\u003c/div\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eUsing AUR (Arch Linux)\\u003c/h3\\u003e\\u003ca id=\\"user-content-using-aur-arch-linux\\" class=\\"anchor\\" aria-label=\\"Permalink: Using AUR (Arch Linux)\\" href=\\"#using-aur-arch-linux\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cdiv class=\\"highlight highlight-source-shell notranslate position-relative overflow-auto\\" dir=\\"auto\\" data-snippet-clipboard-copy-content=\\"# Using yay\\nyay -S opencode-ai-bin\\n\\n# Using paru\\nparu -S opencode-ai-bin\\"\\u003e\\u003cpre\\u003e\\u003cspan class=\\"pl-c\\"\\u003e\\u003cspan class=\\"pl-c\\"\\u003e#\\u003c/span\\u003e Using yay\\u003c/span\\u003e\\nyay -S opencode-ai-bin\\n\\n\\u003cspan class=\\"pl-c\\"\\u003e\\u003cspan class=\\"pl-c\\"\\u003e#\\u003c/span\\u003e Using paru\\u003c/span\\u003e\\nparu -S opencode-ai-bin\\u003c/pre\\u003e\\u003c/div\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eUsing Go\\u003c/h3\\u003e\\u003ca id=\\"user-content-using-go\\" class=\\"anchor\\" aria-label=\\"Permalink: Using Go\\" href=\\"#using-go\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cdiv class=\\"highlight highlight-source-shell notranslate position-relative overflow-auto\\" dir=\\"auto\\" data-snippet-clipboard-copy-content=\\"go install github.com/opencode-ai/opencode@latest\\"\\u003e\\u003cpre\\u003ego install github.com/opencode-ai/opencode@latest\\u003c/pre\\u003e\\u003c/div\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch2 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eConfiguration\\u003c/h2\\u003e\\u003ca id=\\"user-content-configuration\\" class=\\"anchor\\" aria-label=\\"Permalink: Configuration\\" href=\\"#configuration\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eOpenCode looks for configuration in the following locations:\\u003c/p\\u003e\\n\\u003cul dir=\\"auto\\"\\u003e\\n\\u003cli\\u003e\\u003ccode\\u003e$HOME/.opencode.json\\u003c/code\\u003e\\u003c/li\\u003e\\n\\u003cli\\u003e\\u003ccode\\u003e$XDG\_CONFIG\_HOME/opencode/.opencode.json\\u003c/code\\u003e\\u003c/li\\u003e\\n\\u003cli\\u003e\\u003ccode\\u003e./.opencode.json\\u003c/code\\u003e (local directory)\\u003c/li\\u003e\\n\\u003c/ul\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eAuto Compact Feature\\u003c/h3\\u003e\\u003ca id=\\"user-content-auto-compact-feature\\" class=\\"anchor\\" aria-label=\\"Permalink: Auto Compact Feature\\" href=\\"#auto-compact-feature\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eOpenCode includes an auto compact feature that automatically summarizes your conversation when it approaches the model's context window limit. When enabled (default setting), this feature:\\u003c/p\\u003e\\n\\u003cul dir=\\"auto\\"\\u003e\\n\\u003cli\\u003eMonitors token usage during your conversation\\u003c/li\\u003e\\n\\u003cli\\u003eAutomatically triggers summarization when usage reaches 95% of the model's context window\\u003c/li\\u003e\\n\\u003cli\\u003eCreates a new session with the summary, allowing you to continue your work without losing context\\u003c/li\\u003e\\n\\u003cli\\u003eHelps prevent \\"out of context\\" errors that can occur with long conversations\\u003c/li\\u003e\\n\\u003c/ul\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eYou can enable or disable this feature in your configuration file:\\u003c/p\\u003e\\n\\u003cdiv class=\\"highlight highlight-source-json notranslate position-relative overflow-auto\\" dir=\\"auto\\" data-snippet-clipboard-copy-content=\\"{\\n \\u0026quot;autoCompact\\u0026quot;: true // default is true\\n}\\"\\u003e\\u003cpre\\u003e{\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"autoCompact\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-c1\\"\\u003etrue\\u003c/span\\u003e \\u003cspan class=\\"pl-ii\\"\\u003e// default is true\\u003c/span\\u003e\\n}\\u003c/pre\\u003e\\u003c/div\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eEnvironment Variables\\u003c/h3\\u003e\\u003ca id=\\"user-content-environment-variables\\" class=\\"anchor\\" aria-label=\\"Permalink: Environment Variables\\" href=\\"#environment-variables\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eYou can configure OpenCode using environment variables:\\u003c/p\\u003e\\n\\u003cmarkdown-accessiblity-table\\u003e\\u003ctable\\u003e\\n\\u003cthead\\u003e\\n\\u003ctr\\u003e\\n\\u003cth\\u003eEnvironment Variable\\u003c/th\\u003e\\n\\u003cth\\u003ePurpose\\u003c/th\\u003e\\n\\u003c/tr\\u003e\\n\\u003c/thead\\u003e\\n\\u003ctbody\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eANTHROPIC\_API\_KEY\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eFor Claude models\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eOPENAI\_API\_KEY\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eFor OpenAI models\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eGEMINI\_API\_KEY\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eFor Google Gemini models\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eGITHUB\_TOKEN\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eFor Github Copilot models (see \\u003ca href=\\"#using-github-copilot\\"\\u003eUsing Github Copilot\\u003c/a\\u003e)\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eVERTEXAI\_PROJECT\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eFor Google Cloud VertexAI (Gemini)\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eVERTEXAI\_LOCATION\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eFor Google Cloud VertexAI (Gemini)\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eGROQ\_API\_KEY\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eFor Groq models\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eAWS\_ACCESS\_KEY\_ID\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eFor AWS Bedrock (Claude)\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eAWS\_SECRET\_ACCESS\_KEY\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eFor AWS Bedrock (Claude)\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eAWS\_REGION\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eFor AWS Bedrock (Claude)\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eAZURE\_OPENAI\_ENDPOINT\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eFor Azure OpenAI models\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eAZURE\_OPENAI\_API\_KEY\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eFor Azure OpenAI models (optional when using Entra ID)\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eAZURE\_OPENAI\_API\_VERSION\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eFor Azure OpenAI models\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eLOCAL\_ENDPOINT\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eFor self-hosted models\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eSHELL\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eDefault shell to use (if not specified in config)\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003c/tbody\\u003e\\n\\u003c/table\\u003e\\u003c/markdown-accessiblity-table\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eShell Configuration\\u003c/h3\\u003e\\u003ca id=\\"user-content-shell-configuration\\" class=\\"anchor\\" aria-label=\\"Permalink: Shell Configuration\\" href=\\"#shell-configuration\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eOpenCode allows you to configure the shell used by the bash tool. By default, it uses the shell specified in the \\u003ccode\\u003eSHELL\\u003c/code\\u003e environment variable, or falls back to \\u003ccode\\u003e/bin/bash\\u003c/code\\u003e if not set.\\u003c/p\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eYou can override this in your configuration file:\\u003c/p\\u003e\\n\\u003cdiv class=\\"highlight highlight-source-json notranslate position-relative overflow-auto\\" dir=\\"auto\\" data-snippet-clipboard-copy-content=\\"{\\n \\u0026quot;shell\\u0026quot;: {\\n \\u0026quot;path\\u0026quot;: \\u0026quot;/bin/zsh\\u0026quot;,\\n \\u0026quot;args\\u0026quot;: \[\\u0026quot;-l\\u0026quot;\]\\n }\\n}\\"\\u003e\\u003cpre\\u003e{\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"shell\\"\\u003c/span\\u003e: {\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"path\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-s\\"\\u003e\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e/bin/zsh\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e\\u003c/span\\u003e,\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"args\\"\\u003c/span\\u003e: \[\\u003cspan class=\\"pl-s\\"\\u003e\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e-l\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e\\u003c/span\\u003e\]\\n }\\n}\\u003c/pre\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eThis is useful if you want to use a different shell than your default system shell, or if you need to pass specific arguments to the shell.\\u003c/p\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eConfiguration File Structure\\u003c/h3\\u003e\\u003ca id=\\"user-content-configuration-file-structure\\" class=\\"anchor\\" aria-label=\\"Permalink: Configuration File Structure\\" href=\\"#configuration-file-structure\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cdiv class=\\"highlight highlight-source-json notranslate position-relative overflow-auto\\" dir=\\"auto\\" data-snippet-clipboard-copy-content=\\"{\\n \\u0026quot;data\\u0026quot;: {\\n \\u0026quot;directory\\u0026quot;: \\u0026quot;.opencode\\u0026quot;\\n },\\n \\u0026quot;providers\\u0026quot;: {\\n \\u0026quot;openai\\u0026quot;: {\\n \\u0026quot;apiKey\\u0026quot;: \\u0026quot;your-api-key\\u0026quot;,\\n \\u0026quot;disabled\\u0026quot;: false\\n },\\n \\u0026quot;anthropic\\u0026quot;: {\\n \\u0026quot;apiKey\\u0026quot;: \\u0026quot;your-api-key\\u0026quot;,\\n \\u0026quot;disabled\\u0026quot;: false\\n },\\n \\u0026quot;copilot\\u0026quot;: {\\n \\u0026quot;disabled\\u0026quot;: false\\n },\\n \\u0026quot;groq\\u0026quot;: {\\n \\u0026quot;apiKey\\u0026quot;: \\u0026quot;your-api-key\\u0026quot;,\\n \\u0026quot;disabled\\u0026quot;: false\\n },\\n \\u0026quot;openrouter\\u0026quot;: {\\n \\u0026quot;apiKey\\u0026quot;: \\u0026quot;your-api-key\\u0026quot;,\\n \\u0026quot;disabled\\u0026quot;: false\\n }\\n },\\n \\u0026quot;agents\\u0026quot;: {\\n \\u0026quot;coder\\u0026quot;: {\\n \\u0026quot;model\\u0026quot;: \\u0026quot;claude-3.7-sonnet\\u0026quot;,\\n \\u0026quot;maxTokens\\u0026quot;: 5000\\n },\\n \\u0026quot;task\\u0026quot;: {\\n \\u0026quot;model\\u0026quot;: \\u0026quot;claude-3.7-sonnet\\u0026quot;,\\n \\u0026quot;maxTokens\\u0026quot;: 5000\\n },\\n \\u0026quot;title\\u0026quot;: {\\n \\u0026quot;model\\u0026quot;: \\u0026quot;claude-3.7-sonnet\\u0026quot;,\\n \\u0026quot;maxTokens\\u0026quot;: 80\\n }\\n },\\n \\u0026quot;shell\\u0026quot;: {\\n \\u0026quot;path\\u0026quot;: \\u0026quot;/bin/bash\\u0026quot;,\\n \\u0026quot;args\\u0026quot;: \[\\u0026quot;-l\\u0026quot;\]\\n },\\n \\u0026quot;mcpServers\\u0026quot;: {\\n \\u0026quot;example\\u0026quot;: {\\n \\u0026quot;type\\u0026quot;: \\u0026quot;stdio\\u0026quot;,\\n \\u0026quot;command\\u0026quot;: \\u0026quot;path/to/mcp-server\\u0026quot;,\\n \\u0026quot;env\\u0026quot;: \[\],\\n \\u0026quot;args\\u0026quot;: \[\]\\n }\\n },\\n \\u0026quot;lsp\\u0026quot;: {\\n \\u0026quot;go\\u0026quot;: {\\n \\u0026quot;disabled\\u0026quot;: false,\\n \\u0026quot;command\\u0026quot;: \\u0026quot;gopls\\u0026quot;\\n }\\n },\\n \\u0026quot;debug\\u0026quot;: false,\\n \\u0026quot;debugLSP\\u0026quot;: false,\\n \\u0026quot;autoCompact\\u0026quot;: true\\n}\\"\\u003e\\u003cpre\\u003e{\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"data\\"\\u003c/span\\u003e: {\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"directory\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-s\\"\\u003e\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e.opencode\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e\\u003c/span\\u003e\\n },\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"providers\\"\\u003c/span\\u003e: {\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"openai\\"\\u003c/span\\u003e: {\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"apiKey\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-s\\"\\u003e\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003eyour-api-key\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e\\u003c/span\\u003e,\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"disabled\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-c1\\"\\u003efalse\\u003c/span\\u003e\\n },\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"anthropic\\"\\u003c/span\\u003e: {\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"apiKey\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-s\\"\\u003e\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003eyour-api-key\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e\\u003c/span\\u003e,\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"disabled\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-c1\\"\\u003efalse\\u003c/span\\u003e\\n },\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"copilot\\"\\u003c/span\\u003e: {\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"disabled\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-c1\\"\\u003efalse\\u003c/span\\u003e\\n },\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"groq\\"\\u003c/span\\u003e: {\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"apiKey\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-s\\"\\u003e\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003eyour-api-key\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e\\u003c/span\\u003e,\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"disabled\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-c1\\"\\u003efalse\\u003c/span\\u003e\\n },\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"openrouter\\"\\u003c/span\\u003e: {\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"apiKey\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-s\\"\\u003e\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003eyour-api-key\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e\\u003c/span\\u003e,\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"disabled\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-c1\\"\\u003efalse\\u003c/span\\u003e\\n }\\n },\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"agents\\"\\u003c/span\\u003e: {\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"coder\\"\\u003c/span\\u003e: {\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"model\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-s\\"\\u003e\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003eclaude-3.7-sonnet\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e\\u003c/span\\u003e,\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"maxTokens\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-c1\\"\\u003e5000\\u003c/span\\u003e\\n },\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"task\\"\\u003c/span\\u003e: {\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"model\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-s\\"\\u003e\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003eclaude-3.7-sonnet\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e\\u003c/span\\u003e,\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"maxTokens\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-c1\\"\\u003e5000\\u003c/span\\u003e\\n },\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"title\\"\\u003c/span\\u003e: {\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"model\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-s\\"\\u003e\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003eclaude-3.7-sonnet\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e\\u003c/span\\u003e,\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"maxTokens\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-c1\\"\\u003e80\\u003c/span\\u003e\\n }\\n },\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"shell\\"\\u003c/span\\u003e: {\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"path\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-s\\"\\u003e\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e/bin/bash\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e\\u003c/span\\u003e,\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"args\\"\\u003c/span\\u003e: \[\\u003cspan class=\\"pl-s\\"\\u003e\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e-l\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e\\u003c/span\\u003e\]\\n },\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"mcpServers\\"\\u003c/span\\u003e: {\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"example\\"\\u003c/span\\u003e: {\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"type\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-s\\"\\u003e\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003estdio\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e\\u003c/span\\u003e,\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"command\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-s\\"\\u003e\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003epath/to/mcp-server\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e\\u003c/span\\u003e,\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"env\\"\\u003c/span\\u003e: \[\],\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"args\\"\\u003c/span\\u003e: \[\]\\n }\\n },\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"lsp\\"\\u003c/span\\u003e: {\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"go\\"\\u003c/span\\u003e: {\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"disabled\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-c1\\"\\u003efalse\\u003c/span\\u003e,\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"command\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-s\\"\\u003e\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003egopls\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e\\u003c/span\\u003e\\n }\\n },\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"debug\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-c1\\"\\u003efalse\\u003c/span\\u003e,\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"debugLSP\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-c1\\"\\u003efalse\\u003c/span\\u003e,\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"autoCompact\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-c1\\"\\u003etrue\\u003c/span\\u003e\\n}\\u003c/pre\\u003e\\u003c/div\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch2 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eSupported AI Models\\u003c/h2\\u003e\\u003ca id=\\"user-content-supported-ai-models\\" class=\\"anchor\\" aria-label=\\"Permalink: Supported AI Models\\" href=\\"#supported-ai-models\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eOpenCode supports a variety of AI models from different providers:\\u003c/p\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eOpenAI\\u003c/h3\\u003e\\u003ca id=\\"user-content-openai\\" class=\\"anchor\\" aria-label=\\"Permalink: OpenAI\\" href=\\"#openai\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cul dir=\\"auto\\"\\u003e\\n\\u003cli\\u003eGPT-4.1 family (gpt-4.1, gpt-4.1-mini, gpt-4.1-nano)\\u003c/li\\u003e\\n\\u003cli\\u003eGPT-4.5 Preview\\u003c/li\\u003e\\n\\u003cli\\u003eGPT-4o family (gpt-4o, gpt-4o-mini)\\u003c/li\\u003e\\n\\u003cli\\u003eO1 family (o1, o1-pro, o1-mini)\\u003c/li\\u003e\\n\\u003cli\\u003eO3 family (o3, o3-mini)\\u003c/li\\u003e\\n\\u003cli\\u003eO4 Mini\\u003c/li\\u003e\\n\\u003c/ul\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eAnthropic\\u003c/h3\\u003e\\u003ca id=\\"user-content-anthropic\\" class=\\"anchor\\" aria-label=\\"Permalink: Anthropic\\" href=\\"#anthropic\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cul dir=\\"auto\\"\\u003e\\n\\u003cli\\u003eClaude 4 Sonnet\\u003c/li\\u003e\\n\\u003cli\\u003eClaude 4 Opus\\u003c/li\\u003e\\n\\u003cli\\u003eClaude 3.5 Sonnet\\u003c/li\\u003e\\n\\u003cli\\u003eClaude 3.5 Haiku\\u003c/li\\u003e\\n\\u003cli\\u003eClaude 3.7 Sonnet\\u003c/li\\u003e\\n\\u003cli\\u003eClaude 3 Haiku\\u003c/li\\u003e\\n\\u003cli\\u003eClaude 3 Opus\\u003c/li\\u003e\\n\\u003c/ul\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eGitHub Copilot\\u003c/h3\\u003e\\u003ca id=\\"user-content-github-copilot\\" class=\\"anchor\\" aria-label=\\"Permalink: GitHub Copilot\\" href=\\"#github-copilot\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cul dir=\\"auto\\"\\u003e\\n\\u003cli\\u003eGPT-3.5 Turbo\\u003c/li\\u003e\\n\\u003cli\\u003eGPT-4\\u003c/li\\u003e\\n\\u003cli\\u003eGPT-4o\\u003c/li\\u003e\\n\\u003cli\\u003eGPT-4o Mini\\u003c/li\\u003e\\n\\u003cli\\u003eGPT-4.1\\u003c/li\\u003e\\n\\u003cli\\u003eClaude 3.5 Sonnet\\u003c/li\\u003e\\n\\u003cli\\u003eClaude 3.7 Sonnet\\u003c/li\\u003e\\n\\u003cli\\u003eClaude 3.7 Sonnet Thinking\\u003c/li\\u003e\\n\\u003cli\\u003eClaude Sonnet 4\\u003c/li\\u003e\\n\\u003cli\\u003eO1\\u003c/li\\u003e\\n\\u003cli\\u003eO3 Mini\\u003c/li\\u003e\\n\\u003cli\\u003eO4 Mini\\u003c/li\\u003e\\n\\u003cli\\u003eGemini 2.0 Flash\\u003c/li\\u003e\\n\\u003cli\\u003eGemini 2.5 Pro\\u003c/li\\u003e\\n\\u003c/ul\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eGoogle\\u003c/h3\\u003e\\u003ca id=\\"user-content-google\\" class=\\"anchor\\" aria-label=\\"Permalink: Google\\" href=\\"#google\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cul dir=\\"auto\\"\\u003e\\n\\u003cli\\u003eGemini 2.5\\u003c/li\\u003e\\n\\u003cli\\u003eGemini 2.5 Flash\\u003c/li\\u003e\\n\\u003cli\\u003eGemini 2.0 Flash\\u003c/li\\u003e\\n\\u003cli\\u003eGemini 2.0 Flash Lite\\u003c/li\\u003e\\n\\u003c/ul\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eAWS Bedrock\\u003c/h3\\u003e\\u003ca id=\\"user-content-aws-bedrock\\" class=\\"anchor\\" aria-label=\\"Permalink: AWS Bedrock\\" href=\\"#aws-bedrock\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cul dir=\\"auto\\"\\u003e\\n\\u003cli\\u003eClaude 3.7 Sonnet\\u003c/li\\u003e\\n\\u003c/ul\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eGroq\\u003c/h3\\u003e\\u003ca id=\\"user-content-groq\\" class=\\"anchor\\" aria-label=\\"Permalink: Groq\\" href=\\"#groq\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cul dir=\\"auto\\"\\u003e\\n\\u003cli\\u003eLlama 4 Maverick (17b-128e-instruct)\\u003c/li\\u003e\\n\\u003cli\\u003eLlama 4 Scout (17b-16e-instruct)\\u003c/li\\u003e\\n\\u003cli\\u003eQWEN QWQ-32b\\u003c/li\\u003e\\n\\u003cli\\u003eDeepseek R1 distill Llama 70b\\u003c/li\\u003e\\n\\u003cli\\u003eLlama 3.3 70b Versatile\\u003c/li\\u003e\\n\\u003c/ul\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eAzure OpenAI\\u003c/h3\\u003e\\u003ca id=\\"user-content-azure-openai\\" class=\\"anchor\\" aria-label=\\"Permalink: Azure OpenAI\\" href=\\"#azure-openai\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cul dir=\\"auto\\"\\u003e\\n\\u003cli\\u003eGPT-4.1 family (gpt-4.1, gpt-4.1-mini, gpt-4.1-nano)\\u003c/li\\u003e\\n\\u003cli\\u003eGPT-4.5 Preview\\u003c/li\\u003e\\n\\u003cli\\u003eGPT-4o family (gpt-4o, gpt-4o-mini)\\u003c/li\\u003e\\n\\u003cli\\u003eO1 family (o1, o1-mini)\\u003c/li\\u003e\\n\\u003cli\\u003eO3 family (o3, o3-mini)\\u003c/li\\u003e\\n\\u003cli\\u003eO4 Mini\\u003c/li\\u003e\\n\\u003c/ul\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eGoogle Cloud VertexAI\\u003c/h3\\u003e\\u003ca id=\\"user-content-google-cloud-vertexai\\" class=\\"anchor\\" aria-label=\\"Permalink: Google Cloud VertexAI\\" href=\\"#google-cloud-vertexai\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cul dir=\\"auto\\"\\u003e\\n\\u003cli\\u003eGemini 2.5\\u003c/li\\u003e\\n\\u003cli\\u003eGemini 2.5 Flash\\u003c/li\\u003e\\n\\u003c/ul\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch2 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eUsage\\u003c/h2\\u003e\\u003ca id=\\"user-content-usage\\" class=\\"anchor\\" aria-label=\\"Permalink: Usage\\" href=\\"#usage\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cdiv class=\\"highlight highlight-source-shell notranslate position-relative overflow-auto\\" dir=\\"auto\\" data-snippet-clipboard-copy-content=\\"# Start OpenCode\\nopencode\\n\\n# Start with debug logging\\nopencode -d\\n\\n# Start with a specific working directory\\nopencode -c /path/to/project\\"\\u003e\\u003cpre\\u003e\\u003cspan class=\\"pl-c\\"\\u003e\\u003cspan class=\\"pl-c\\"\\u003e#\\u003c/span\\u003e Start OpenCode\\u003c/span\\u003e\\nopencode\\n\\n\\u003cspan class=\\"pl-c\\"\\u003e\\u003cspan class=\\"pl-c\\"\\u003e#\\u003c/span\\u003e Start with debug logging\\u003c/span\\u003e\\nopencode -d\\n\\n\\u003cspan class=\\"pl-c\\"\\u003e\\u003cspan class=\\"pl-c\\"\\u003e#\\u003c/span\\u003e Start with a specific working directory\\u003c/span\\u003e\\nopencode -c /path/to/project\\u003c/pre\\u003e\\u003c/div\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch2 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eNon-interactive Prompt Mode\\u003c/h2\\u003e\\u003ca id=\\"user-content-non-interactive-prompt-mode\\" class=\\"anchor\\" aria-label=\\"Permalink: Non-interactive Prompt Mode\\" href=\\"#non-interactive-prompt-mode\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eYou can run OpenCode in non-interactive mode by passing a prompt directly as a command-line argument. This is useful for scripting, automation, or when you want a quick answer without launching the full TUI.\\u003c/p\\u003e\\n\\u003cdiv class=\\"highlight highlight-source-shell notranslate position-relative overflow-auto\\" dir=\\"auto\\" data-snippet-clipboard-copy-content=\\"# Run a single prompt and print the AI's response to the terminal\\nopencode -p \\u0026quot;Explain the use of context in Go\\u0026quot;\\n\\n# Get response in JSON format\\nopencode -p \\u0026quot;Explain the use of context in Go\\u0026quot; -f json\\n\\n# Run without showing the spinner (useful for scripts)\\nopencode -p \\u0026quot;Explain the use of context in Go\\u0026quot; -q\\"\\u003e\\u003cpre\\u003e\\u003cspan class=\\"pl-c\\"\\u003e\\u003cspan class=\\"pl-c\\"\\u003e#\\u003c/span\\u003e Run a single prompt and print the AI's response to the terminal\\u003c/span\\u003e\\nopencode -p \\u003cspan class=\\"pl-s\\"\\u003e\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003eExplain the use of context in Go\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e\\u003c/span\\u003e\\n\\n\\u003cspan class=\\"pl-c\\"\\u003e\\u003cspan class=\\"pl-c\\"\\u003e#\\u003c/span\\u003e Get response in JSON format\\u003c/span\\u003e\\nopencode -p \\u003cspan class=\\"pl-s\\"\\u003e\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003eExplain the use of context in Go\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e\\u003c/span\\u003e -f json\\n\\n\\u003cspan class=\\"pl-c\\"\\u003e\\u003cspan class=\\"pl-c\\"\\u003e#\\u003c/span\\u003e Run without showing the spinner (useful for scripts)\\u003c/span\\u003e\\nopencode -p \\u003cspan class=\\"pl-s\\"\\u003e\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003eExplain the use of context in Go\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e\\u003c/span\\u003e -q\\u003c/pre\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eIn this mode, OpenCode will process your prompt, print the result to standard output, and then exit. All permissions are auto-approved for the session.\\u003c/p\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eBy default, a spinner animation is displayed while the model is processing your query. You can disable this spinner with the \\u003ccode\\u003e-q\\u003c/code\\u003e or \\u003ccode\\u003e--quiet\\u003c/code\\u003e flag, which is particularly useful when running OpenCode from scripts or automated workflows.\\u003c/p\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eOutput Formats\\u003c/h3\\u003e\\u003ca id=\\"user-content-output-formats\\" class=\\"anchor\\" aria-label=\\"Permalink: Output Formats\\" href=\\"#output-formats\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eOpenCode supports the following output formats in non-interactive mode:\\u003c/p\\u003e\\n\\u003cmarkdown-accessiblity-table\\u003e\\u003ctable\\u003e\\n\\u003cthead\\u003e\\n\\u003ctr\\u003e\\n\\u003cth\\u003eFormat\\u003c/th\\u003e\\n\\u003cth\\u003eDescription\\u003c/th\\u003e\\n\\u003c/tr\\u003e\\n\\u003c/thead\\u003e\\n\\u003ctbody\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003etext\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003ePlain text output (default)\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003ejson\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eOutput wrapped in a JSON object\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003c/tbody\\u003e\\n\\u003c/table\\u003e\\u003c/markdown-accessiblity-table\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eThe output format is implemented as a strongly-typed \\u003ccode\\u003eOutputFormat\\u003c/code\\u003e in the codebase, ensuring type safety and validation when processing outputs.\\u003c/p\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch2 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eCommand-line Flags\\u003c/h2\\u003e\\u003ca id=\\"user-content-command-line-flags\\" class=\\"anchor\\" aria-label=\\"Permalink: Command-line Flags\\" href=\\"#command-line-flags\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cmarkdown-accessiblity-table\\u003e\\u003ctable\\u003e\\n\\u003cthead\\u003e\\n\\u003ctr\\u003e\\n\\u003cth\\u003eFlag\\u003c/th\\u003e\\n\\u003cth\\u003eShort\\u003c/th\\u003e\\n\\u003cth\\u003eDescription\\u003c/th\\u003e\\n\\u003c/tr\\u003e\\n\\u003c/thead\\u003e\\n\\u003ctbody\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003e--help\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003e-h\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eDisplay help information\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003e--debug\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003e-d\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eEnable debug mode\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003e--cwd\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003e-c\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eSet current working directory\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003e--prompt\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003e-p\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eRun a single prompt in non-interactive mode\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003e--output-format\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003e-f\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eOutput format for non-interactive mode (text, json)\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003e--quiet\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003e-q\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eHide spinner in non-interactive mode\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003c/tbody\\u003e\\n\\u003c/table\\u003e\\u003c/markdown-accessiblity-table\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch2 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eKeyboard Shortcuts\\u003c/h2\\u003e\\u003ca id=\\"user-content-keyboard-shortcuts\\" class=\\"anchor\\" aria-label=\\"Permalink: Keyboard Shortcuts\\" href=\\"#keyboard-shortcuts\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eGlobal Shortcuts\\u003c/h3\\u003e\\u003ca id=\\"user-content-global-shortcuts\\" class=\\"anchor\\" aria-label=\\"Permalink: Global Shortcuts\\" href=\\"#global-shortcuts\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cmarkdown-accessiblity-table\\u003e\\u003ctable\\u003e\\n\\u003cthead\\u003e\\n\\u003ctr\\u003e\\n\\u003cth\\u003eShortcut\\u003c/th\\u003e\\n\\u003cth\\u003eAction\\u003c/th\\u003e\\n\\u003c/tr\\u003e\\n\\u003c/thead\\u003e\\n\\u003ctbody\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eCtrl+C\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eQuit application\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eCtrl+?\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eToggle help dialog\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003e?\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eToggle help dialog (when not in editing mode)\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eCtrl+L\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eView logs\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eCtrl+A\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eSwitch session\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eCtrl+K\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eCommand dialog\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eCtrl+O\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eToggle model selection dialog\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eEsc\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eClose current overlay/dialog or return to previous mode\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003c/tbody\\u003e\\n\\u003c/table\\u003e\\u003c/markdown-accessiblity-table\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eChat Page Shortcuts\\u003c/h3\\u003e\\u003ca id=\\"user-content-chat-page-shortcuts\\" class=\\"anchor\\" aria-label=\\"Permalink: Chat Page Shortcuts\\" href=\\"#chat-page-shortcuts\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cmarkdown-accessiblity-table\\u003e\\u003ctable\\u003e\\n\\u003cthead\\u003e\\n\\u003ctr\\u003e\\n\\u003cth\\u003eShortcut\\u003c/th\\u003e\\n\\u003cth\\u003eAction\\u003c/th\\u003e\\n\\u003c/tr\\u003e\\n\\u003c/thead\\u003e\\n\\u003ctbody\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eCtrl+N\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eCreate new session\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eCtrl+X\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eCancel current operation/generation\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003ei\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eFocus editor (when not in writing mode)\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eEsc\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eExit writing mode and focus messages\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003c/tbody\\u003e\\n\\u003c/table\\u003e\\u003c/markdown-accessiblity-table\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eEditor Shortcuts\\u003c/h3\\u003e\\u003ca id=\\"user-content-editor-shortcuts\\" class=\\"anchor\\" aria-label=\\"Permalink: Editor Shortcuts\\" href=\\"#editor-shortcuts\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cmarkdown-accessiblity-table\\u003e\\u003ctable\\u003e\\n\\u003cthead\\u003e\\n\\u003ctr\\u003e\\n\\u003cth\\u003eShortcut\\u003c/th\\u003e\\n\\u003cth\\u003eAction\\u003c/th\\u003e\\n\\u003c/tr\\u003e\\n\\u003c/thead\\u003e\\n\\u003ctbody\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eCtrl+S\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eSend message (when editor is focused)\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eEnter\\u003c/code\\u003e or \\u003ccode\\u003eCtrl+S\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eSend message (when editor is not focused)\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eCtrl+E\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eOpen external editor\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eEsc\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eBlur editor and focus messages\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003c/tbody\\u003e\\n\\u003c/table\\u003e\\u003c/markdown-accessiblity-table\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eSession Dialog Shortcuts\\u003c/h3\\u003e\\u003ca id=\\"user-content-session-dialog-shortcuts\\" class=\\"anchor\\" aria-label=\\"Permalink: Session Dialog Shortcuts\\" href=\\"#session-dialog-shortcuts\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cmarkdown-accessiblity-table\\u003e\\u003ctable\\u003e\\n\\u003cthead\\u003e\\n\\u003ctr\\u003e\\n\\u003cth\\u003eShortcut\\u003c/th\\u003e\\n\\u003cth\\u003eAction\\u003c/th\\u003e\\n\\u003c/tr\\u003e\\n\\u003c/thead\\u003e\\n\\u003ctbody\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003e↑\\u003c/code\\u003e or \\u003ccode\\u003ek\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003ePrevious session\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003e↓\\u003c/code\\u003e or \\u003ccode\\u003ej\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eNext session\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eEnter\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eSelect session\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eEsc\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eClose dialog\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003c/tbody\\u003e\\n\\u003c/table\\u003e\\u003c/markdown-accessiblity-table\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eModel Dialog Shortcuts\\u003c/h3\\u003e\\u003ca id=\\"user-content-model-dialog-shortcuts\\" class=\\"anchor\\" aria-label=\\"Permalink: Model Dialog Shortcuts\\" href=\\"#model-dialog-shortcuts\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cmarkdown-accessiblity-table\\u003e\\u003ctable\\u003e\\n\\u003cthead\\u003e\\n\\u003ctr\\u003e\\n\\u003cth\\u003eShortcut\\u003c/th\\u003e\\n\\u003cth\\u003eAction\\u003c/th\\u003e\\n\\u003c/tr\\u003e\\n\\u003c/thead\\u003e\\n\\u003ctbody\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003e↑\\u003c/code\\u003e or \\u003ccode\\u003ek\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eMove up\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003e↓\\u003c/code\\u003e or \\u003ccode\\u003ej\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eMove down\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003e←\\u003c/code\\u003e or \\u003ccode\\u003eh\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003ePrevious provider\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003e→\\u003c/code\\u003e or \\u003ccode\\u003el\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eNext provider\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eEsc\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eClose dialog\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003c/tbody\\u003e\\n\\u003c/table\\u003e\\u003c/markdown-accessiblity-table\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003ePermission Dialog Shortcuts\\u003c/h3\\u003e\\u003ca id=\\"user-content-permission-dialog-shortcuts\\" class=\\"anchor\\" aria-label=\\"Permalink: Permission Dialog Shortcuts\\" href=\\"#permission-dialog-shortcuts\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cmarkdown-accessiblity-table\\u003e\\u003ctable\\u003e\\n\\u003cthead\\u003e\\n\\u003ctr\\u003e\\n\\u003cth\\u003eShortcut\\u003c/th\\u003e\\n\\u003cth\\u003eAction\\u003c/th\\u003e\\n\\u003c/tr\\u003e\\n\\u003c/thead\\u003e\\n\\u003ctbody\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003e←\\u003c/code\\u003e or \\u003ccode\\u003eleft\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eSwitch options left\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003e→\\u003c/code\\u003e or \\u003ccode\\u003eright\\u003c/code\\u003e or \\u003ccode\\u003etab\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eSwitch options right\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eEnter\\u003c/code\\u003e or \\u003ccode\\u003espace\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eConfirm selection\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003ea\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eAllow permission\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eA\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eAllow permission for session\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003ed\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eDeny permission\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003c/tbody\\u003e\\n\\u003c/table\\u003e\\u003c/markdown-accessiblity-table\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eLogs Page Shortcuts\\u003c/h3\\u003e\\u003ca id=\\"user-content-logs-page-shortcuts\\" class=\\"anchor\\" aria-label=\\"Permalink: Logs Page Shortcuts\\" href=\\"#logs-page-shortcuts\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cmarkdown-accessiblity-table\\u003e\\u003ctable\\u003e\\n\\u003cthead\\u003e\\n\\u003ctr\\u003e\\n\\u003cth\\u003eShortcut\\u003c/th\\u003e\\n\\u003cth\\u003eAction\\u003c/th\\u003e\\n\\u003c/tr\\u003e\\n\\u003c/thead\\u003e\\n\\u003ctbody\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eBackspace\\u003c/code\\u003e or \\u003ccode\\u003eq\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eReturn to chat page\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003c/tbody\\u003e\\n\\u003c/table\\u003e\\u003c/markdown-accessiblity-table\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch2 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eAI Assistant Tools\\u003c/h2\\u003e\\u003ca id=\\"user-content-ai-assistant-tools\\" class=\\"anchor\\" aria-label=\\"Permalink: AI Assistant Tools\\" href=\\"#ai-assistant-tools\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eOpenCode's AI assistant has access to various tools to help with coding tasks:\\u003c/p\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eFile and Code Tools\\u003c/h3\\u003e\\u003ca id=\\"user-content-file-and-code-tools\\" class=\\"anchor\\" aria-label=\\"Permalink: File and Code Tools\\" href=\\"#file-and-code-tools\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cmarkdown-accessiblity-table\\u003e\\u003ctable\\u003e\\n\\u003cthead\\u003e\\n\\u003ctr\\u003e\\n\\u003cth\\u003eTool\\u003c/th\\u003e\\n\\u003cth\\u003eDescription\\u003c/th\\u003e\\n\\u003cth\\u003eParameters\\u003c/th\\u003e\\n\\u003c/tr\\u003e\\n\\u003c/thead\\u003e\\n\\u003ctbody\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eglob\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eFind files by pattern\\u003c/td\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003epattern\\u003c/code\\u003e (required), \\u003ccode\\u003epath\\u003c/code\\u003e (optional)\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003egrep\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eSearch file contents\\u003c/td\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003epattern\\u003c/code\\u003e (required), \\u003ccode\\u003epath\\u003c/code\\u003e (optional), \\u003ccode\\u003einclude\\u003c/code\\u003e (optional), \\u003ccode\\u003eliteral\_text\\u003c/code\\u003e (optional)\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003els\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eList directory contents\\u003c/td\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003epath\\u003c/code\\u003e (optional), \\u003ccode\\u003eignore\\u003c/code\\u003e (optional array of patterns)\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eview\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eView file contents\\u003c/td\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003efile\_path\\u003c/code\\u003e (required), \\u003ccode\\u003eoffset\\u003c/code\\u003e (optional), \\u003ccode\\u003elimit\\u003c/code\\u003e (optional)\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003ewrite\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eWrite to files\\u003c/td\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003efile\_path\\u003c/code\\u003e (required), \\u003ccode\\u003econtent\\u003c/code\\u003e (required)\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eedit\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eEdit files\\u003c/td\\u003e\\n\\u003ctd\\u003eVarious parameters for file editing\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003epatch\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eApply patches to files\\u003c/td\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003efile\_path\\u003c/code\\u003e (required), \\u003ccode\\u003ediff\\u003c/code\\u003e (required)\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003ediagnostics\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eGet diagnostics information\\u003c/td\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003efile\_path\\u003c/code\\u003e (optional)\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003c/tbody\\u003e\\n\\u003c/table\\u003e\\u003c/markdown-accessiblity-table\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eOther Tools\\u003c/h3\\u003e\\u003ca id=\\"user-content-other-tools\\" class=\\"anchor\\" aria-label=\\"Permalink: Other Tools\\" href=\\"#other-tools\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cmarkdown-accessiblity-table\\u003e\\u003ctable\\u003e\\n\\u003cthead\\u003e\\n\\u003ctr\\u003e\\n\\u003cth\\u003eTool\\u003c/th\\u003e\\n\\u003cth\\u003eDescription\\u003c/th\\u003e\\n\\u003cth\\u003eParameters\\u003c/th\\u003e\\n\\u003c/tr\\u003e\\n\\u003c/thead\\u003e\\n\\u003ctbody\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003ebash\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eExecute shell commands\\u003c/td\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003ecommand\\u003c/code\\u003e (required), \\u003ccode\\u003etimeout\\u003c/code\\u003e (optional)\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003efetch\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eFetch data from URLs\\u003c/td\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eurl\\u003c/code\\u003e (required), \\u003ccode\\u003eformat\\u003c/code\\u003e (required), \\u003ccode\\u003etimeout\\u003c/code\\u003e (optional)\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003esourcegraph\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eSearch code across public repositories\\u003c/td\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003equery\\u003c/code\\u003e (required), \\u003ccode\\u003ecount\\u003c/code\\u003e (optional), \\u003ccode\\u003econtext\_window\\u003c/code\\u003e (optional), \\u003ccode\\u003etimeout\\u003c/code\\u003e (optional)\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eagent\\u003c/code\\u003e\\u003c/td\\u003e\\n\\u003ctd\\u003eRun sub-tasks with the AI agent\\u003c/td\\u003e\\n\\u003ctd\\u003e\\u003ccode\\u003eprompt\\u003c/code\\u003e (required)\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003c/tbody\\u003e\\n\\u003c/table\\u003e\\u003c/markdown-accessiblity-table\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch2 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eArchitecture\\u003c/h2\\u003e\\u003ca id=\\"user-content-architecture\\" class=\\"anchor\\" aria-label=\\"Permalink: Architecture\\" href=\\"#architecture\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eOpenCode is built with a modular architecture:\\u003c/p\\u003e\\n\\u003cul dir=\\"auto\\"\\u003e\\n\\u003cli\\u003e\\u003cstrong\\u003ecmd\\u003c/strong\\u003e: Command-line interface using Cobra\\u003c/li\\u003e\\n\\u003cli\\u003e\\u003cstrong\\u003einternal/app\\u003c/strong\\u003e: Core application services\\u003c/li\\u003e\\n\\u003cli\\u003e\\u003cstrong\\u003einternal/config\\u003c/strong\\u003e: Configuration management\\u003c/li\\u003e\\n\\u003cli\\u003e\\u003cstrong\\u003einternal/db\\u003c/strong\\u003e: Database operations and migrations\\u003c/li\\u003e\\n\\u003cli\\u003e\\u003cstrong\\u003einternal/llm\\u003c/strong\\u003e: LLM providers and tools integration\\u003c/li\\u003e\\n\\u003cli\\u003e\\u003cstrong\\u003einternal/tui\\u003c/strong\\u003e: Terminal UI components and layouts\\u003c/li\\u003e\\n\\u003cli\\u003e\\u003cstrong\\u003einternal/logging\\u003c/strong\\u003e: Logging infrastructure\\u003c/li\\u003e\\n\\u003cli\\u003e\\u003cstrong\\u003einternal/message\\u003c/strong\\u003e: Message handling\\u003c/li\\u003e\\n\\u003cli\\u003e\\u003cstrong\\u003einternal/session\\u003c/strong\\u003e: Session management\\u003c/li\\u003e\\n\\u003cli\\u003e\\u003cstrong\\u003einternal/lsp\\u003c/strong\\u003e: Language Server Protocol integration\\u003c/li\\u003e\\n\\u003c/ul\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch2 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eCustom Commands\\u003c/h2\\u003e\\u003ca id=\\"user-content-custom-commands\\" class=\\"anchor\\" aria-label=\\"Permalink: Custom Commands\\" href=\\"#custom-commands\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eOpenCode supports custom commands that can be created by users to quickly send predefined prompts to the AI assistant.\\u003c/p\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eCreating Custom Commands\\u003c/h3\\u003e\\u003ca id=\\"user-content-creating-custom-commands\\" class=\\"anchor\\" aria-label=\\"Permalink: Creating Custom Commands\\" href=\\"#creating-custom-commands\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eCustom commands are predefined prompts stored as Markdown files in one of three locations:\\u003c/p\\u003e\\n\\u003col dir=\\"auto\\"\\u003e\\n\\u003cli\\u003e\\n\\u003cp dir=\\"auto\\"\\u003e\\u003cstrong\\u003eUser Commands\\u003c/strong\\u003e (prefixed with \\u003ccode\\u003euser:\\u003c/code\\u003e):\\u003c/p\\u003e\\n\\u003cdiv class=\\"snippet-clipboard-content notranslate position-relative overflow-auto\\" data-snippet-clipboard-copy-content=\\"$XDG\_CONFIG\_HOME/opencode/commands/\\"\\u003e\\u003cpre class=\\"notranslate\\"\\u003e\\u003ccode\\u003e$XDG\_CONFIG\_HOME/opencode/commands/\\n\\u003c/code\\u003e\\u003c/pre\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003e(typically \\u003ccode\\u003e~/.config/opencode/commands/\\u003c/code\\u003e on Linux/macOS)\\u003c/p\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eor\\u003c/p\\u003e\\n\\u003cdiv class=\\"snippet-clipboard-content notranslate position-relative overflow-auto\\" data-snippet-clipboard-copy-content=\\"$HOME/.opencode/commands/\\"\\u003e\\u003cpre class=\\"notranslate\\"\\u003e\\u003ccode\\u003e$HOME/.opencode/commands/\\n\\u003c/code\\u003e\\u003c/pre\\u003e\\u003c/div\\u003e\\n\\u003c/li\\u003e\\n\\u003cli\\u003e\\n\\u003cp dir=\\"auto\\"\\u003e\\u003cstrong\\u003eProject Commands\\u003c/strong\\u003e (prefixed with \\u003ccode\\u003eproject:\\u003c/code\\u003e):\\u003c/p\\u003e\\n\\u003cdiv class=\\"snippet-clipboard-content notranslate position-relative overflow-auto\\" data-snippet-clipboard-copy-content=\\"\\u0026lt;PROJECT DIR\\u0026gt;/.opencode/commands/\\"\\u003e\\u003cpre class=\\"notranslate\\"\\u003e\\u003ccode\\u003e\\u0026lt;PROJECT DIR\\u0026gt;/.opencode/commands/\\n\\u003c/code\\u003e\\u003c/pre\\u003e\\u003c/div\\u003e\\n\\u003c/li\\u003e\\n\\u003c/ol\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eEach \\u003ccode\\u003e.md\\u003c/code\\u003e file in these directories becomes a custom command. The file name (without extension) becomes the command ID.\\u003c/p\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eFor example, creating a file at \\u003ccode\\u003e~/.config/opencode/commands/prime-context.md\\u003c/code\\u003e with content:\\u003c/p\\u003e\\n\\u003cdiv class=\\"highlight highlight-text-md notranslate position-relative overflow-auto\\" dir=\\"auto\\" data-snippet-clipboard-copy-content=\\"RUN git ls-files\\nREAD README.md\\"\\u003e\\u003cpre\\u003eRUN git ls-files\\nREAD README.md\\u003c/pre\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eThis creates a command called \\u003ccode\\u003euser:prime-context\\u003c/code\\u003e.\\u003c/p\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eCommand Arguments\\u003c/h3\\u003e\\u003ca id=\\"user-content-command-arguments\\" class=\\"anchor\\" aria-label=\\"Permalink: Command Arguments\\" href=\\"#command-arguments\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eOpenCode supports named arguments in custom commands using placeholders in the format \\u003ccode\\u003e$NAME\\u003c/code\\u003e (where NAME consists of uppercase letters, numbers, and underscores, and must start with a letter).\\u003c/p\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eFor example:\\u003c/p\\u003e\\n\\u003cdiv class=\\"highlight highlight-text-md notranslate position-relative overflow-auto\\" dir=\\"auto\\" data-snippet-clipboard-copy-content=\\"# Fetch Context for Issue $ISSUE\_NUMBER\\n\\nRUN gh issue view $ISSUE\_NUMBER --json title,body,comments\\nRUN git grep --author=\\u0026quot;$AUTHOR\_NAME\\u0026quot; -n .\\nRUN grep -R \\u0026quot;$SEARCH\_PATTERN\\u0026quot; $DIRECTORY\\"\\u003e\\u003cpre\\u003e\\u003cspan class=\\"pl-mh\\"\\u003e# \\u003cspan class=\\"pl-en\\"\\u003eFetch Context for Issue $ISSUE\_NUMBER\\u003c/span\\u003e\\u003c/span\\u003e\\n\\nRUN gh issue view $ISSUE\_NUMBER --json title,body,comments\\nRUN git grep --author=\\"$AUTHOR\_NAME\\" -n .\\nRUN grep -R \\"$SEARCH\_PATTERN\\" $DIRECTORY\\u003c/pre\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eWhen you run a command with arguments, OpenCode will prompt you to enter values for each unique placeholder. Named arguments provide several benefits:\\u003c/p\\u003e\\n\\u003cul dir=\\"auto\\"\\u003e\\n\\u003cli\\u003eClear identification of what each argument represents\\u003c/li\\u003e\\n\\u003cli\\u003eAbility to use the same argument multiple times\\u003c/li\\u003e\\n\\u003cli\\u003eBetter organization for commands with multiple inputs\\u003c/li\\u003e\\n\\u003c/ul\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eOrganizing Commands\\u003c/h3\\u003e\\u003ca id=\\"user-content-organizing-commands\\" class=\\"anchor\\" aria-label=\\"Permalink: Organizing Commands\\" href=\\"#organizing-commands\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eYou can organize commands in subdirectories:\\u003c/p\\u003e\\n\\u003cdiv class=\\"snippet-clipboard-content notranslate position-relative overflow-auto\\" data-snippet-clipboard-copy-content=\\"~/.config/opencode/commands/git/commit.md\\"\\u003e\\u003cpre class=\\"notranslate\\"\\u003e\\u003ccode\\u003e~/.config/opencode/commands/git/commit.md\\n\\u003c/code\\u003e\\u003c/pre\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eThis creates a command with ID \\u003ccode\\u003euser:git:commit\\u003c/code\\u003e.\\u003c/p\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eUsing Custom Commands\\u003c/h3\\u003e\\u003ca id=\\"user-content-using-custom-commands\\" class=\\"anchor\\" aria-label=\\"Permalink: Using Custom Commands\\" href=\\"#using-custom-commands\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003col dir=\\"auto\\"\\u003e\\n\\u003cli\\u003ePress \\u003ccode\\u003eCtrl+K\\u003c/code\\u003e to open the command dialog\\u003c/li\\u003e\\n\\u003cli\\u003eSelect your custom command (prefixed with either \\u003ccode\\u003euser:\\u003c/code\\u003e or \\u003ccode\\u003eproject:\\u003c/code\\u003e)\\u003c/li\\u003e\\n\\u003cli\\u003ePress Enter to execute the command\\u003c/li\\u003e\\n\\u003c/ol\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eThe content of the command file will be sent as a message to the AI assistant.\\u003c/p\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eBuilt-in Commands\\u003c/h3\\u003e\\u003ca id=\\"user-content-built-in-commands\\" class=\\"anchor\\" aria-label=\\"Permalink: Built-in Commands\\" href=\\"#built-in-commands\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eOpenCode includes several built-in commands:\\u003c/p\\u003e\\n\\u003cmarkdown-accessiblity-table\\u003e\\u003ctable\\u003e\\n\\u003cthead\\u003e\\n\\u003ctr\\u003e\\n\\u003cth\\u003eCommand\\u003c/th\\u003e\\n\\u003cth\\u003eDescription\\u003c/th\\u003e\\n\\u003c/tr\\u003e\\n\\u003c/thead\\u003e\\n\\u003ctbody\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003eInitialize Project\\u003c/td\\u003e\\n\\u003ctd\\u003eCreates or updates the OpenCode.md memory file with project-specific information\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003ctr\\u003e\\n\\u003ctd\\u003eCompact Session\\u003c/td\\u003e\\n\\u003ctd\\u003eManually triggers the summarization of the current session, creating a new session with the summary\\u003c/td\\u003e\\n\\u003c/tr\\u003e\\n\\u003c/tbody\\u003e\\n\\u003c/table\\u003e\\u003c/markdown-accessiblity-table\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch2 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eMCP (Model Context Protocol)\\u003c/h2\\u003e\\u003ca id=\\"user-content-mcp-model-context-protocol\\" class=\\"anchor\\" aria-label=\\"Permalink: MCP (Model Context Protocol)\\" href=\\"#mcp-model-context-protocol\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eOpenCode implements the Model Context Protocol (MCP) to extend its capabilities through external tools. MCP provides a standardized way for the AI assistant to interact with external services and tools.\\u003c/p\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eMCP Features\\u003c/h3\\u003e\\u003ca id=\\"user-content-mcp-features\\" class=\\"anchor\\" aria-label=\\"Permalink: MCP Features\\" href=\\"#mcp-features\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cul dir=\\"auto\\"\\u003e\\n\\u003cli\\u003e\\u003cstrong\\u003eExternal Tool Integration\\u003c/strong\\u003e: Connect to external tools and services via a standardized protocol\\u003c/li\\u003e\\n\\u003cli\\u003e\\u003cstrong\\u003eTool Discovery\\u003c/strong\\u003e: Automatically discover available tools from MCP servers\\u003c/li\\u003e\\n\\u003cli\\u003e\\u003cstrong\\u003eMultiple Connection Types\\u003c/strong\\u003e:\\n\\u003cul dir=\\"auto\\"\\u003e\\n\\u003cli\\u003e\\u003cstrong\\u003eStdio\\u003c/strong\\u003e: Communicate with tools via standard input/output\\u003c/li\\u003e\\n\\u003cli\\u003e\\u003cstrong\\u003eSSE\\u003c/strong\\u003e: Communicate with tools via Server-Sent Events\\u003c/li\\u003e\\n\\u003c/ul\\u003e\\n\\u003c/li\\u003e\\n\\u003cli\\u003e\\u003cstrong\\u003eSecurity\\u003c/strong\\u003e: Permission system for controlling access to MCP tools\\u003c/li\\u003e\\n\\u003c/ul\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eConfiguring MCP Servers\\u003c/h3\\u003e\\u003ca id=\\"user-content-configuring-mcp-servers\\" class=\\"anchor\\" aria-label=\\"Permalink: Configuring MCP Servers\\" href=\\"#configuring-mcp-servers\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eMCP servers are defined in the configuration file under the \\u003ccode\\u003emcpServers\\u003c/code\\u003e section:\\u003c/p\\u003e\\n\\u003cdiv class=\\"highlight highlight-source-json notranslate position-relative overflow-auto\\" dir=\\"auto\\" data-snippet-clipboard-copy-content=\\"{\\n \\u0026quot;mcpServers\\u0026quot;: {\\n \\u0026quot;example\\u0026quot;: {\\n \\u0026quot;type\\u0026quot;: \\u0026quot;stdio\\u0026quot;,\\n \\u0026quot;command\\u0026quot;: \\u0026quot;path/to/mcp-server\\u0026quot;,\\n \\u0026quot;env\\u0026quot;: \[\],\\n \\u0026quot;args\\u0026quot;: \[\]\\n },\\n \\u0026quot;web-example\\u0026quot;: {\\n \\u0026quot;type\\u0026quot;: \\u0026quot;sse\\u0026quot;,\\n \\u0026quot;url\\u0026quot;: \\u0026quot;https://example.com/mcp\\u0026quot;,\\n \\u0026quot;headers\\u0026quot;: {\\n \\u0026quot;Authorization\\u0026quot;: \\u0026quot;Bearer token\\u0026quot;\\n }\\n }\\n }\\n}\\"\\u003e\\u003cpre\\u003e{\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"mcpServers\\"\\u003c/span\\u003e: {\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"example\\"\\u003c/span\\u003e: {\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"type\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-s\\"\\u003e\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003estdio\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e\\u003c/span\\u003e,\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"command\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-s\\"\\u003e\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003epath/to/mcp-server\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e\\u003c/span\\u003e,\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"env\\"\\u003c/span\\u003e: \[\],\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"args\\"\\u003c/span\\u003e: \[\]\\n },\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"web-example\\"\\u003c/span\\u003e: {\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"type\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-s\\"\\u003e\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003esse\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e\\u003c/span\\u003e,\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"url\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-s\\"\\u003e\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003ehttps://example.com/mcp\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e\\u003c/span\\u003e,\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"headers\\"\\u003c/span\\u003e: {\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"Authorization\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-s\\"\\u003e\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003eBearer token\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e\\u003c/span\\u003e\\n }\\n }\\n }\\n}\\u003c/pre\\u003e\\u003c/div\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eMCP Tool Usage\\u003c/h3\\u003e\\u003ca id=\\"user-content-mcp-tool-usage\\" class=\\"anchor\\" aria-label=\\"Permalink: MCP Tool Usage\\" href=\\"#mcp-tool-usage\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eOnce configured, MCP tools are automatically available to the AI assistant alongside built-in tools. They follow the same permission model as other tools, requiring user approval before execution.\\u003c/p\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch2 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eLSP (Language Server Protocol)\\u003c/h2\\u003e\\u003ca id=\\"user-content-lsp-language-server-protocol\\" class=\\"anchor\\" aria-label=\\"Permalink: LSP (Language Server Protocol)\\" href=\\"#lsp-language-server-protocol\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eOpenCode integrates with Language Server Protocol to provide code intelligence features across multiple programming languages.\\u003c/p\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eLSP Features\\u003c/h3\\u003e\\u003ca id=\\"user-content-lsp-features\\" class=\\"anchor\\" aria-label=\\"Permalink: LSP Features\\" href=\\"#lsp-features\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cul dir=\\"auto\\"\\u003e\\n\\u003cli\\u003e\\u003cstrong\\u003eMulti-language Support\\u003c/strong\\u003e: Connect to language servers for different programming languages\\u003c/li\\u003e\\n\\u003cli\\u003e\\u003cstrong\\u003eDiagnostics\\u003c/strong\\u003e: Receive error checking and linting information\\u003c/li\\u003e\\n\\u003cli\\u003e\\u003cstrong\\u003eFile Watching\\u003c/strong\\u003e: Automatically notify language servers of file changes\\u003c/li\\u003e\\n\\u003c/ul\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eConfiguring LSP\\u003c/h3\\u003e\\u003ca id=\\"user-content-configuring-lsp\\" class=\\"anchor\\" aria-label=\\"Permalink: Configuring LSP\\" href=\\"#configuring-lsp\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eLanguage servers are configured in the configuration file under the \\u003ccode\\u003elsp\\u003c/code\\u003e section:\\u003c/p\\u003e\\n\\u003cdiv class=\\"highlight highlight-source-json notranslate position-relative overflow-auto\\" dir=\\"auto\\" data-snippet-clipboard-copy-content=\\"{\\n \\u0026quot;lsp\\u0026quot;: {\\n \\u0026quot;go\\u0026quot;: {\\n \\u0026quot;disabled\\u0026quot;: false,\\n \\u0026quot;command\\u0026quot;: \\u0026quot;gopls\\u0026quot;\\n },\\n \\u0026quot;typescript\\u0026quot;: {\\n \\u0026quot;disabled\\u0026quot;: false,\\n \\u0026quot;command\\u0026quot;: \\u0026quot;typescript-language-server\\u0026quot;,\\n \\u0026quot;args\\u0026quot;: \[\\u0026quot;--stdio\\u0026quot;\]\\n }\\n }\\n}\\"\\u003e\\u003cpre\\u003e{\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"lsp\\"\\u003c/span\\u003e: {\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"go\\"\\u003c/span\\u003e: {\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"disabled\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-c1\\"\\u003efalse\\u003c/span\\u003e,\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"command\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-s\\"\\u003e\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003egopls\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e\\u003c/span\\u003e\\n },\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"typescript\\"\\u003c/span\\u003e: {\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"disabled\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-c1\\"\\u003efalse\\u003c/span\\u003e,\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"command\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-s\\"\\u003e\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003etypescript-language-server\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e\\u003c/span\\u003e,\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"args\\"\\u003c/span\\u003e: \[\\u003cspan class=\\"pl-s\\"\\u003e\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e--stdio\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e\\u003c/span\\u003e\]\\n }\\n }\\n}\\u003c/pre\\u003e\\u003c/div\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eLSP Integration with AI\\u003c/h3\\u003e\\u003ca id=\\"user-content-lsp-integration-with-ai\\" class=\\"anchor\\" aria-label=\\"Permalink: LSP Integration with AI\\" href=\\"#lsp-integration-with-ai\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eThe AI assistant can access LSP features through the \\u003ccode\\u003ediagnostics\\u003c/code\\u003e tool, allowing it to:\\u003c/p\\u003e\\n\\u003cul dir=\\"auto\\"\\u003e\\n\\u003cli\\u003eCheck for errors in your code\\u003c/li\\u003e\\n\\u003cli\\u003eSuggest fixes based on diagnostics\\u003c/li\\u003e\\n\\u003c/ul\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eWhile the LSP client implementation supports the full LSP protocol (including completions, hover, definition, etc.), currently only diagnostics are exposed to the AI assistant.\\u003c/p\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch2 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eUsing Github Copilot\\u003c/h2\\u003e\\u003ca id=\\"user-content-using-github-copilot\\" class=\\"anchor\\" aria-label=\\"Permalink: Using Github Copilot\\" href=\\"#using-github-copilot\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003e\\u003cem\\u003eCopilot support is currently experimental.\\u003c/em\\u003e\\u003c/p\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eRequirements\\u003c/h3\\u003e\\u003ca id=\\"user-content-requirements\\" class=\\"anchor\\" aria-label=\\"Permalink: Requirements\\" href=\\"#requirements\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cul dir=\\"auto\\"\\u003e\\n\\u003cli\\u003e\\u003ca href=\\"https://github.com/settings/copilot\\"\\u003eCopilot chat in the IDE\\u003c/a\\u003e enabled in GitHub settings\\u003c/li\\u003e\\n\\u003cli\\u003eOne of:\\n\\u003cul dir=\\"auto\\"\\u003e\\n\\u003cli\\u003eVSCode Github Copilot chat extension\\u003c/li\\u003e\\n\\u003cli\\u003eGithub \\u003ccode\\u003egh\\u003c/code\\u003e CLI\\u003c/li\\u003e\\n\\u003cli\\u003eNeovim Github Copilot plugin (\\u003ccode\\u003ecopilot.vim\\u003c/code\\u003e or \\u003ccode\\u003ecopilot.lua\\u003c/code\\u003e)\\u003c/li\\u003e\\n\\u003cli\\u003eGithub token with copilot permissions\\u003c/li\\u003e\\n\\u003c/ul\\u003e\\n\\u003c/li\\u003e\\n\\u003c/ul\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eIf using one of the above plugins or cli tools, make sure you use the authenticate\\nthe tool with your github account. This should create a github token at one of the following locations:\\u003c/p\\u003e\\n\\u003cul dir=\\"auto\\"\\u003e\\n\\u003cli\\u003e~/.config/github-copilot/\[hosts,apps\].json\\u003c/li\\u003e\\n\\u003cli\\u003e$XDG\_CONFIG\_HOME/github-copilot/\[hosts,apps\].json\\u003c/li\\u003e\\n\\u003c/ul\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eIf using an explicit github token, you may either set the $GITHUB\_TOKEN environment variable or add it to the opencode.json config file at \\u003ccode\\u003eproviders.copilot.apiKey\\u003c/code\\u003e.\\u003c/p\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch2 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eUsing a self-hosted model provider\\u003c/h2\\u003e\\u003ca id=\\"user-content-using-a-self-hosted-model-provider\\" class=\\"anchor\\" aria-label=\\"Permalink: Using a self-hosted model provider\\" href=\\"#using-a-self-hosted-model-provider\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eOpenCode can also load and use models from a self-hosted (OpenAI-like) provider.\\nThis is useful for developers who want to experiment with custom models.\\u003c/p\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eConfiguring a self-hosted provider\\u003c/h3\\u003e\\u003ca id=\\"user-content-configuring-a-self-hosted-provider\\" class=\\"anchor\\" aria-label=\\"Permalink: Configuring a self-hosted provider\\" href=\\"#configuring-a-self-hosted-provider\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eYou can use a self-hosted model by setting the \\u003ccode\\u003eLOCAL\_ENDPOINT\\u003c/code\\u003e environment variable.\\nThis will cause OpenCode to load and use the models from the specified endpoint.\\u003c/p\\u003e\\n\\u003cdiv class=\\"highlight highlight-source-shell notranslate position-relative overflow-auto\\" dir=\\"auto\\" data-snippet-clipboard-copy-content=\\"LOCAL\_ENDPOINT=http://localhost:1235/v1\\"\\u003e\\u003cpre\\u003eLOCAL\_ENDPOINT=http://localhost:1235/v1\\u003c/pre\\u003e\\u003c/div\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eConfiguring a self-hosted model\\u003c/h3\\u003e\\u003ca id=\\"user-content-configuring-a-self-hosted-model\\" class=\\"anchor\\" aria-label=\\"Permalink: Configuring a self-hosted model\\" href=\\"#configuring-a-self-hosted-model\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eYou can also configure a self-hosted model in the configuration file under the \\u003ccode\\u003eagents\\u003c/code\\u003e section:\\u003c/p\\u003e\\n\\u003cdiv class=\\"highlight highlight-source-json notranslate position-relative overflow-auto\\" dir=\\"auto\\" data-snippet-clipboard-copy-content=\\"{\\n \\u0026quot;agents\\u0026quot;: {\\n \\u0026quot;coder\\u0026quot;: {\\n \\u0026quot;model\\u0026quot;: \\u0026quot;local.granite-3.3-2b-instruct@q8\_0\\u0026quot;,\\n \\u0026quot;reasoningEffort\\u0026quot;: \\u0026quot;high\\u0026quot;\\n }\\n }\\n}\\"\\u003e\\u003cpre\\u003e{\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"agents\\"\\u003c/span\\u003e: {\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"coder\\"\\u003c/span\\u003e: {\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"model\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-s\\"\\u003e\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003elocal.granite-3.3-2b-instruct@q8\_0\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e\\u003c/span\\u003e,\\n \\u003cspan class=\\"pl-ent\\"\\u003e\\"reasoningEffort\\"\\u003c/span\\u003e: \\u003cspan class=\\"pl-s\\"\\u003e\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003ehigh\\u003cspan class=\\"pl-pds\\"\\u003e\\"\\u003c/span\\u003e\\u003c/span\\u003e\\n }\\n }\\n}\\u003c/pre\\u003e\\u003c/div\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch2 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eDevelopment\\u003c/h2\\u003e\\u003ca id=\\"user-content-development\\" class=\\"anchor\\" aria-label=\\"Permalink: Development\\" href=\\"#development\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003ePrerequisites\\u003c/h3\\u003e\\u003ca id=\\"user-content-prerequisites\\" class=\\"anchor\\" aria-label=\\"Permalink: Prerequisites\\" href=\\"#prerequisites\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cul dir=\\"auto\\"\\u003e\\n\\u003cli\\u003eGo 1.24.0 or higher\\u003c/li\\u003e\\n\\u003c/ul\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch3 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eBuilding from Source\\u003c/h3\\u003e\\u003ca id=\\"user-content-building-from-source\\" class=\\"anchor\\" aria-label=\\"Permalink: Building from Source\\" href=\\"#building-from-source\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cdiv class=\\"highlight highlight-source-shell notranslate position-relative overflow-auto\\" dir=\\"auto\\" data-snippet-clipboard-copy-content=\\"# Clone the repository\\ngit clone https://github.com/opencode-ai/opencode.git\\ncd opencode\\n\\n# Build\\ngo build -o opencode\\n\\n# Run\\n./opencode\\"\\u003e\\u003cpre\\u003e\\u003cspan class=\\"pl-c\\"\\u003e\\u003cspan class=\\"pl-c\\"\\u003e#\\u003c/span\\u003e Clone the repository\\u003c/span\\u003e\\ngit clone https://github.com/opencode-ai/opencode.git\\n\\u003cspan class=\\"pl-c1\\"\\u003ecd\\u003c/span\\u003e opencode\\n\\n\\u003cspan class=\\"pl-c\\"\\u003e\\u003cspan class=\\"pl-c\\"\\u003e#\\u003c/span\\u003e Build\\u003c/span\\u003e\\ngo build -o opencode\\n\\n\\u003cspan class=\\"pl-c\\"\\u003e\\u003cspan class=\\"pl-c\\"\\u003e#\\u003c/span\\u003e Run\\u003c/span\\u003e\\n./opencode\\u003c/pre\\u003e\\u003c/div\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch2 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eAcknowledgments\\u003c/h2\\u003e\\u003ca id=\\"user-content-acknowledgments\\" class=\\"anchor\\" aria-label=\\"Permalink: Acknowledgments\\" href=\\"#acknowledgments\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eOpenCode gratefully acknowledges the contributions and support from these key individuals:\\u003c/p\\u003e\\n\\u003cul dir=\\"auto\\"\\u003e\\n\\u003cli\\u003e\\u003ca href=\\"https://github.com/isaacphi\\"\\u003e@isaacphi\\u003c/a\\u003e - For the \\u003ca href=\\"https://github.com/isaacphi/mcp-language-server\\"\\u003emcp-language-server\\u003c/a\\u003e project which provided the foundation for our LSP client implementation\\u003c/li\\u003e\\n\\u003cli\\u003e\\u003ca href=\\"https://github.com/adamdottv\\"\\u003e@adamdottv\\u003c/a\\u003e - For the design direction and UI/UX architecture\\u003c/li\\u003e\\n\\u003c/ul\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eSpecial thanks to the broader open source community whose tools and libraries have made this project possible.\\u003c/p\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch2 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eLicense\\u003c/h2\\u003e\\u003ca id=\\"user-content-license\\" class=\\"anchor\\" aria-label=\\"Permalink: License\\" href=\\"#license\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eOpenCode is licensed under the MIT License. See the \\u003ca href=\\"/opencode-ai/opencode/blob/main/LICENSE\\"\\u003eLICENSE\\u003c/a\\u003e file for details.\\u003c/p\\u003e\\n\\u003cdiv class=\\"markdown-heading\\" dir=\\"auto\\"\\u003e\\u003ch2 tabindex=\\"-1\\" class=\\"heading-element\\" dir=\\"auto\\"\\u003eContributing\\u003c/h2\\u003e\\u003ca id=\\"user-content-contributing\\" class=\\"anchor\\" aria-label=\\"Permalink: Contributing\\" href=\\"#contributing\\"\\u003e\\u003csvg class=\\"octicon octicon-link\\" viewBox=\\"0 0 16 16\\" version=\\"1.1\\" width=\\"16\\" height=\\"16\\" aria-hidden=\\"true\\"\\u003e\\u003cpath d=\\"m7.775 3.275 1.25-1.25a3.5 3.5 0 1 1 4.95 4.95l-2.5 2.5a3.5 3.5 0 0 1-4.95 0 .751.751 0 0 1 .018-1.042.751.751 0 0 1 1.042-.018 1.998 1.998 0 0 0 2.83 0l2.5-2.5a2.002 2.002 0 0 0-2.83-2.83l-1.25 1.25a.751.751 0 0 1-1.042-.018.751.751 0 0 1-.018-1.042Zm-4.69 9.64a1.998 1.998 0 0 0 2.83 0l1.25-1.25a.751.751 0 0 1 1.042.018.751.751 0 0 1 .018 1.042l-1.25 1.25a3.5 3.5 0 1 1-4.95-4.95l2.5-2.5a3.5 3.5 0 0 1 4.95 0 .751.751 0 0 1-.018 1.042.751.751 0 0 1-1.042.018 1.998 1.998 0 0 0-2.83 0l-2.5 2.5a1.998 1.998 0 0 0 0 2.83Z\\"\\u003e\\u003c/path\\u003e\\u003c/svg\\u003e\\u003c/a\\u003e\\u003c/div\\u003e\\n\\u003cp dir=\\"auto\\"\\u003eContributions are welcome! Here's how you can contribute:\\u003c/p\\u003e\\n\\u003col dir=\\"auto\\"\\u003e\\n\\u003cli\\u003eFork the repository\\u003c/li\\u003e\\n\\u003cli\\u003eCreate a feature branch (\\u003ccode\\u003egit checkout -b feature/amazing-feature\\u003c/code\\u003e)\\u003c/li\\u003e\\n\\u003cli\\u003eCommit your changes (\\u003ccode\\u003egit commit -m 'Add some amazing feature'\\u003c/code\\u003e)\\u003c/li\\u003e\\n\\u003cli\\u003ePush to the branch (\\u003ccode\\u003egit push origin feature/amazing-feature\\u003c/code\\u003e)\\u003c/li\\u003e\\n\\u003cli\\u003eOpen a Pull Request\\u003c/li\\u003e\\n\\u003c/ol\\u003e\\n\\u003cp dir=\\"auto\\"\\u003ePlease make sure to update tests as appropriate and follow the existing code style.\\u003c/p\\u003e\\n\\u003c/article\\u003e","loaded":true,"timedOut":false,"errorMessage":null,"headerInfo":{"toc":\[{"level":1,"text":"⌬ OpenCode","anchor":"-opencode","htmlText":"⌬ OpenCode"},{"level":2,"text":"Overview","anchor":"overview","htmlText":"Overview"},{"level":2,"text":"Features","anchor":"features","htmlText":"Features"},{"level":2,"text":"Installation","anchor":"installation","htmlText":"Installation"},{"level":3,"text":"Using the Install Script","anchor":"using-the-install-script","htmlText":"Using the Install Script"},{"level":3,"text":"Using Homebrew (macOS and Linux)","anchor":"using-homebrew-macos-and-linux","htmlText":"Using Homebrew (macOS and Linux)"},{"level":3,"text":"Using AUR (Arch Linux)","anchor":"using-aur-arch-linux","htmlText":"Using AUR (Arch Linux)"},{"level":3,"text":"Using Go","anchor":"using-go","htmlText":"Using Go"},{"level":2,"text":"Configuration","anchor":"configuration","htmlText":"Configuration"},{"level":3,"text":"Auto Compact Feature","anchor":"auto-compact-feature","htmlText":"Auto Compact Feature"},{"level":3,"text":"Environment Variables","anchor":"environment-variables","htmlText":"Environment Variables"},{"level":3,"text":"Shell Configuration","anchor":"shell-configuration","htmlText":"Shell Configuration"},{"level":3,"text":"Configuration File Structure","anchor":"configuration-file-structure","htmlText":"Configuration File Structure"},{"level":2,"text":"Supported AI Models","anchor":"supported-ai-models","htmlText":"Supported AI Models"},{"level":3,"text":"OpenAI","anchor":"openai","htmlText":"OpenAI"},{"level":3,"text":"Anthropic","anchor":"anthropic","htmlText":"Anthropic"},{"level":3,"text":"GitHub Copilot","anchor":"github-copilot","htmlText":"GitHub Copilot"},{"level":3,"text":"Google","anchor":"google","htmlText":"Google"},{"level":3,"text":"AWS Bedrock","anchor":"aws-bedrock","htmlText":"AWS Bedrock"},{"level":3,"text":"Groq","anchor":"groq","htmlText":"Groq"},{"level":3,"text":"Azure OpenAI","anchor":"azure-openai","htmlText":"Azure OpenAI"},{"level":3,"text":"Google Cloud VertexAI","anchor":"google-cloud-vertexai","htmlText":"Google Cloud VertexAI"},{"level":2,"text":"Usage","anchor":"usage","htmlText":"Usage"},{"level":2,"text":"Non-interactive Prompt Mode","anchor":"non-interactive-prompt-mode","htmlText":"Non-interactive Prompt Mode"},{"level":3,"text":"Output Formats","anchor":"output-formats","htmlText":"Output Formats"},{"level":2,"text":"Command-line Flags","anchor":"command-line-flags","htmlText":"Command-line Flags"},{"level":2,"text":"Keyboard Shortcuts","anchor":"keyboard-shortcuts","htmlText":"Keyboard Shortcuts"},{"level":3,"text":"Global Shortcuts","anchor":"global-shortcuts","htmlText":"Global Shortcuts"},{"level":3,"text":"Chat Page Shortcuts","anchor":"chat-page-shortcuts","htmlText":"Chat Page Shortcuts"},{"level":3,"text":"Editor Shortcuts","anchor":"editor-shortcuts","htmlText":"Editor Shortcuts"},{"level":3,"text":"Session Dialog Shortcuts","anchor":"session-dialog-shortcuts","htmlText":"Session Dialog Shortcuts"},{"level":3,"text":"Model Dialog Shortcuts","anchor":"model-dialog-shortcuts","htmlText":"Model Dialog Shortcuts"},{"level":3,"text":"Permission Dialog Shortcuts","anchor":"permission-dialog-shortcuts","htmlText":"Permission Dialog Shortcuts"},{"level":3,"text":"Logs Page Shortcuts","anchor":"logs-page-shortcuts","htmlText":"Logs Page Shortcuts"},{"level":2,"text":"AI Assistant Tools","anchor":"ai-assistant-tools","htmlText":"AI Assistant Tools"},{"level":3,"text":"File and Code Tools","anchor":"file-and-code-tools","htmlText":"File and Code Tools"},{"level":3,"text":"Other Tools","anchor":"other-tools","htmlText":"Other Tools"},{"level":2,"text":"Architecture","anchor":"architecture","htmlText":"Architecture"},{"level":2,"text":"Custom Commands","anchor":"custom-commands","htmlText":"Custom Commands"},{"level":3,"text":"Creating Custom Commands","anchor":"creating-custom-commands","htmlText":"Creating Custom Commands"},{"level":3,"text":"Command Arguments","anchor":"command-arguments","htmlText":"Command Arguments"},{"level":3,"text":"Organizing Commands","anchor":"organizing-commands","htmlText":"Organizing Commands"},{"level":3,"text":"Using Custom Commands","anchor":"using-custom-commands","htmlText":"Using Custom Commands"},{"level":3,"text":"Built-in Commands","anchor":"built-in-commands","htmlText":"Built-in Commands"},{"level":2,"text":"MCP (Model Context Protocol)","anchor":"mcp-model-context-protocol","htmlText":"MCP (Model Context Protocol)"},{"level":3,"text":"MCP Features","anchor":"mcp-features","htmlText":"MCP Features"},{"level":3,"text":"Configuring MCP Servers","anchor":"configuring-mcp-servers","htmlText":"Configuring MCP Servers"},{"level":3,"text":"MCP Tool Usage","anchor":"mcp-tool-usage","htmlText":"MCP Tool Usage"},{"level":2,"text":"LSP (Language Server Protocol)","anchor":"lsp-language-server-protocol","htmlText":"LSP (Language Server Protocol)"},{"level":3,"text":"LSP Features","anchor":"lsp-features","htmlText":"LSP Features"},{"level":3,"text":"Configuring LSP","anchor":"configuring-lsp","htmlText":"Configuring LSP"},{"level":3,"text":"LSP Integration with AI","anchor":"lsp-integration-with-ai","htmlText":"LSP Integration with AI"},{"level":2,"text":"Using Github Copilot","anchor":"using-github-copilot","htmlText":"Using Github Copilot"},{"level":3,"text":"Requirements","anchor":"requirements","htmlText":"Requirements"},{"level":2,"text":"Using a self-hosted model provider","anchor":"using-a-self-hosted-model-provider","htmlText":"Using a self-hosted model provider"},{"level":3,"text":"Configuring a self-hosted provider","anchor":"configuring-a-self-hosted-provider","htmlText":"Configuring a self-hosted provider"},{"level":3,"text":"Configuring a self-hosted model","anchor":"configuring-a-self-hosted-model","htmlText":"Configuring a self-hosted model"},{"level":2,"text":"Development","anchor":"development","htmlText":"Development"},{"level":3,"text":"Prerequisites","anchor":"prerequisites","htmlText":"Prerequisites"},{"level":3,"text":"Building from Source","anchor":"building-from-source","htmlText":"Building from Source"},{"level":2,"text":"Acknowledgments","anchor":"acknowledgments","htmlText":"Acknowledgments"},{"level":2,"text":"License","anchor":"license","htmlText":"License"},{"level":2,"text":"Contributing","anchor":"contributing","htmlText":"Contributing"}\],"siteNavLoginPath":"/login?return\_to=https%3A%2F%2Fgithub.com%2Fopencode-ai%2Fopencode"}},{"displayName":"LICENSE","repoName":"opencode","refName":"main","path":"LICENSE","preferredFileType":"license","tabName":"MIT","richText":null,"loaded":false,"timedOut":false,"errorMessage":null,"headerInfo":{"toc":null,"siteNavLoginPath":"/login?return\_to=https%3A%2F%2Fgithub.com%2Fopencode-ai%2Fopencode"}}\],"overviewFilesProcessingTime":0}},"appPayload":{"helpUrl":"https://docs.github.com","findFileWorkerPath":"/assets-cdn/worker/find-file-worker-263cab1760dd.js","findInFileWorkerPath":"/assets-cdn/worker/find-in-file-worker-b84e9496fc59.js","githubDevUrl":null,"enabled\_features":{"copilot\_workspace":null,"code\_nav\_ui\_events":false,"react\_blob\_overlay":false,"accessible\_code\_button":true}}}}

 main

[**25** Branches](/opencode-ai/opencode/branches)[**55** Tags](/opencode-ai/opencode/tags)

[](/opencode-ai/opencode/branches)[](/opencode-ai/opencode/tags)

Go to file

Code

Open more actions menu

Folders and files
-----------------

Name

Name

Last commit message

Last commit date

Latest commit
-------------

[![aldehir](https://avatars.githubusercontent.com/u/765227?v=4&size=40)](/aldehir)[aldehir](/opencode-ai/opencode/commits?author=aldehir)

[fix(tool/grep): always show file names with rg (](/opencode-ai/opencode/commit/f0571f5f5adef12eba9ddf6d07223a043d63dca8)[#271](https://github.com/opencode-ai/opencode/pull/271)[)](/opencode-ai/opencode/commit/f0571f5f5adef12eba9ddf6d07223a043d63dca8)

success

Jul 1, 2025

[f0571f5](/opencode-ai/opencode/commit/f0571f5f5adef12eba9ddf6d07223a043d63dca8) · Jul 1, 2025

History
-------

[183 Commits](/opencode-ai/opencode/commits/main/)

Open commit details

[](/opencode-ai/opencode/commits/main/)

[.github/workflows](/opencode-ai/opencode/tree/main/.github/workflows "This path skips through empty directories")

[.github/workflows](/opencode-ai/opencode/tree/main/.github/workflows "This path skips through empty directories")

[remove cache](/opencode-ai/opencode/commit/7648a2d790f66d90afbd929fbb9eb738b8eaa6cd "remove cache")

Apr 23, 2025

[cmd](/opencode-ai/opencode/tree/main/cmd "cmd")

[cmd](/opencode-ai/opencode/tree/main/cmd "cmd")

[refactor: upgrade Anthropic SDK to v1.4.0 and adapt provider code](/opencode-ai/opencode/commit/05b0570f48204ede62bf5be839c1b3a45858ebd1 "refactor: upgrade Anthropic SDK to v1.4.0 and adapt provider code
### CHANGES
- Upgrade Anthropic Go SDK dependency to version 1.4.0.
- Adapt provider code to breaking changes in the SDK.
- Update constructors for tool use and thinking parameters.
- Use new `OfText` field for accessing message content.
- Add Claude 4 Opus and Sonnet to documentation.")

Jun 7, 2025

[internal](/opencode-ai/opencode/tree/main/internal "internal")

[internal](/opencode-ai/opencode/tree/main/internal "internal")

[fix(tool/grep): always show file names with rg (](/opencode-ai/opencode/commit/f0571f5f5adef12eba9ddf6d07223a043d63dca8 "fix(tool/grep): always show file names with rg (#271)")[#271](https://github.com/opencode-ai/opencode/pull/271)[)](/opencode-ai/opencode/commit/f0571f5f5adef12eba9ddf6d07223a043d63dca8 "fix(tool/grep): always show file names with rg (#271)")

Jul 1, 2025

[scripts](/opencode-ai/opencode/tree/main/scripts "scripts")

[scripts](/opencode-ai/opencode/tree/main/scripts "scripts")

[Context Window Warning (](/opencode-ai/opencode/commit/90084ce43d7a44c4dea98705694f34d01dbe192a "Context Window Warning (#152)
* context window warning & compact command
* auto compact
* fix permissions
* update readme
* fix 3.5 context window
* small update
* remove unused interface
* remove unused msg")[#152](https://github.com/opencode-ai/opencode/pull/152)[)](/opencode-ai/opencode/commit/90084ce43d7a44c4dea98705694f34d01dbe192a "Context Window Warning (#152)
* context window warning & compact command
* auto compact
* fix permissions
* update readme
* fix 3.5 context window
* small update
* remove unused interface
* remove unused msg")

May 9, 2025

[.gitignore](/opencode-ai/opencode/blob/main/.gitignore ".gitignore")

[.gitignore](/opencode-ai/opencode/blob/main/.gitignore ".gitignore")

[feat: add github copilot provider (](/opencode-ai/opencode/commit/b9bedbae80046a5ae03be38e897bba96661b28d2 "feat: add github copilot provider (#230)
* feat: add github copilot
* fix: add support for claude4")[#230](https://github.com/opencode-ai/opencode/pull/230)[)](/opencode-ai/opencode/commit/b9bedbae80046a5ae03be38e897bba96661b28d2 "feat: add github copilot provider (#230)
* feat: add github copilot
* fix: add support for claude4")

Jun 25, 2025

[.goreleaser.yml](/opencode-ai/opencode/blob/main/.goreleaser.yml ".goreleaser.yml")

[.goreleaser.yml](/opencode-ai/opencode/blob/main/.goreleaser.yml ".goreleaser.yml")

[update aur](/opencode-ai/opencode/commit/16103e013cf88aa5e4adb2fbf4e8928e27fb167b "update aur")

May 14, 2025

[.opencode.json](/opencode-ai/opencode/blob/main/.opencode.json ".opencode.json")

[.opencode.json](/opencode-ai/opencode/blob/main/.opencode.json ".opencode.json")

[config validation](/opencode-ai/opencode/commit/a8d5787e8ef561037f73b669128f46ae1b1e8553 "config validation")

Apr 21, 2025

[LICENSE](/opencode-ai/opencode/blob/main/LICENSE "LICENSE")

[LICENSE](/opencode-ai/opencode/blob/main/LICENSE "LICENSE")

[add license](/opencode-ai/opencode/commit/e3a62736db3f16c4d2b55a9eeb6b080b2c625a83 "add license")

Apr 21, 2025

[README.md](/opencode-ai/opencode/blob/main/README.md "README.md")

[README.md](/opencode-ai/opencode/blob/main/README.md "README.md")

[feat: add github copilot provider (](/opencode-ai/opencode/commit/b9bedbae80046a5ae03be38e897bba96661b28d2 "feat: add github copilot provider (#230)
* feat: add github copilot
* fix: add support for claude4")[#230](https://github.com/opencode-ai/opencode/pull/230)[)](/opencode-ai/opencode/commit/b9bedbae80046a5ae03be38e897bba96661b28d2 "feat: add github copilot provider (#230)
* feat: add github copilot
* fix: add support for claude4")

Jun 25, 2025

[go.mod](/opencode-ai/opencode/blob/main/go.mod "go.mod")

[go.mod](/opencode-ai/opencode/blob/main/go.mod "go.mod")

[refactor: upgrade Anthropic SDK to v1.4.0 and adapt provider code](/opencode-ai/opencode/commit/05b0570f48204ede62bf5be839c1b3a45858ebd1 "refactor: upgrade Anthropic SDK to v1.4.0 and adapt provider code
### CHANGES
- Upgrade Anthropic Go SDK dependency to version 1.4.0.
- Adapt provider code to breaking changes in the SDK.
- Update constructors for tool use and thinking parameters.
- Use new `OfText` field for accessing message content.
- Add Claude 4 Opus and Sonnet to documentation.")

Jun 7, 2025

[go.sum](/opencode-ai/opencode/blob/main/go.sum "go.sum")

[go.sum](/opencode-ai/opencode/blob/main/go.sum "go.sum")

[refactor: upgrade Anthropic SDK to v1.4.0 and adapt provider code](/opencode-ai/opencode/commit/05b0570f48204ede62bf5be839c1b3a45858ebd1 "refactor: upgrade Anthropic SDK to v1.4.0 and adapt provider code
### CHANGES
- Upgrade Anthropic Go SDK dependency to version 1.4.0.
- Adapt provider code to breaking changes in the SDK.
- Update constructors for tool use and thinking parameters.
- Use new `OfText` field for accessing message content.
- Add Claude 4 Opus and Sonnet to documentation.")

Jun 7, 2025

[install](/opencode-ai/opencode/blob/main/install "install")

[install](/opencode-ai/opencode/blob/main/install "install")

[sync](/opencode-ai/opencode/commit/1b22acbc58ffd67b3a42d01bfc320edcb68e5fc7 "sync")

Apr 23, 2025

[main.go](/opencode-ai/opencode/blob/main/main.go "main.go")

[main.go](/opencode-ai/opencode/blob/main/main.go "main.go")

[change package name](/opencode-ai/opencode/commit/b106787a50935a2c10ce6a2f01d77dd0aae80642 "change package name")

Apr 24, 2025

[opencode-schema.json](/opencode-ai/opencode/blob/main/opencode-schema.json "opencode-schema.json")

[opencode-schema.json](/opencode-ai/opencode/blob/main/opencode-schema.json "opencode-schema.json")

[feat: add github copilot provider (](/opencode-ai/opencode/commit/b9bedbae80046a5ae03be38e897bba96661b28d2 "feat: add github copilot provider (#230)
* feat: add github copilot
* fix: add support for claude4")[#230](https://github.com/opencode-ai/opencode/pull/230)[)](/opencode-ai/opencode/commit/b9bedbae80046a5ae03be38e897bba96661b28d2 "feat: add github copilot provider (#230)
* feat: add github copilot
* fix: add support for claude4")

Jun 25, 2025

[sqlc.yaml](/opencode-ai/opencode/blob/main/sqlc.yaml "sqlc.yaml")

[sqlc.yaml](/opencode-ai/opencode/blob/main/sqlc.yaml "sqlc.yaml")

[add initial stuff](/opencode-ai/opencode/commit/8daa6e774a6e02698c90392e7b2008542f789594 "add initial stuff")

Mar 23, 2025

View all files

Repository files navigation
---------------------------

*   [README](#)
*   [MIT license](#)

Note

This is the original OpenCode repository, now continuing at [Charm](https://github.com/charmbracelet) with its original creator, [Kujtim Hoxha](https://github.com/kujtimiihoxha).  
Development is continuing under a new name as we prepare for a public relaunch.  
Follow [@charmcli](https://x.com/charmcli) or join our [Discord](https://charm.sh/chat) for updates.

⌬ OpenCode
==========

[](#-opencode)

 [![](https://private-user-images.githubusercontent.com/25087/444283386-9ae61ef6-70e5-4876-bc45-5bcb4e52c714.gif?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HMoQsTdMIHSPPLp7o_X_Q4d_d_TMurG2SrSTeNxi5a8)](https://private-user-images.githubusercontent.com/25087/444283386-9ae61ef6-70e5-4876-bc45-5bcb4e52c714.gif?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HMoQsTdMIHSPPLp7o_X_Q4d_d_TMurG2SrSTeNxi5a8) [![444283386-9ae61ef6-70e5-4876-bc45-5bcb4e52c714.gif?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HMoQsTdMIHSPPLp7o_X_Q4d_d_TMurG2SrSTeNxi5a8](https://private-user-images.githubusercontent.com/25087/444283386-9ae61ef6-70e5-4876-bc45-5bcb4e52c714.gif?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HMoQsTdMIHSPPLp7o_X_Q4d_d_TMurG2SrSTeNxi5a8)

](https://private-user-images.githubusercontent.com/25087/444283386-9ae61ef6-70e5-4876-bc45-5bcb4e52c714.gif?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HMoQsTdMIHSPPLp7o_X_Q4d_d_TMurG2SrSTeNxi5a8)[](https://private-user-images.githubusercontent.com/25087/444283386-9ae61ef6-70e5-4876-bc45-5bcb4e52c714.gif?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.HMoQsTdMIHSPPLp7o_X_Q4d_d_TMurG2SrSTeNxi5a8)

> **⚠️ Early Development Notice:** This project is in early development and is not yet ready for production use. Features may change, break, or be incomplete. Use at your own risk.

A powerful terminal-based AI assistant for developers, providing intelligent coding assistance directly in your terminal.

Overview
--------

[](#overview)

OpenCode is a Go-based CLI application that brings AI assistance to your terminal. It provides a TUI (Terminal User Interface) for interacting with various AI models to help with coding tasks, debugging, and more.

For a quick video overview, check out [![](https://camo.githubusercontent.com/3744c496d91bb014d72b7ef2dc4c827f2c1d41ec9c01cb8883be3cdb5ca486ce/68747470733a2f2f75706c6f61642e77696b696d656469612e6f72672f77696b6970656469612f636f6d6d6f6e732f302f30392f596f75547562655f66756c6c2d636f6c6f725f69636f6e5f253238323031372532392e737667) OpenCode + Gemini 2.5 Pro: BYE Claude Code! I'm SWITCHING To the FASTEST AI Coder!](https://www.youtube.com/watch?v=P8luPmEa1QI)

[![](https://camo.githubusercontent.com/02859f4e0c37f4f66f06a62ffe41a98cfceead2387dd083cbb52c574942696b5/68747470733a2f2f69332e7974696d672e636f6d2f76692f50386c75506d45613151492f6d617872657364656661756c742e6a7067)](https://www.youtube.com/watch?v=P8luPmEa1QI)

Features
--------

[](#features)

*   **Interactive TUI**: Built with [Bubble Tea](https://github.com/charmbracelet/bubbletea) for a smooth terminal experience
*   **Multiple AI Providers**: Support for OpenAI, Anthropic Claude, Google Gemini, AWS Bedrock, Groq, Azure OpenAI, and OpenRouter
*   **Session Management**: Save and manage multiple conversation sessions
*   **Tool Integration**: AI can execute commands, search files, and modify code
*   **Vim-like Editor**: Integrated editor with text input capabilities
*   **Persistent Storage**: SQLite database for storing conversations and sessions
*   **LSP Integration**: Language Server Protocol support for code intelligence
*   **File Change Tracking**: Track and visualize file changes during sessions
*   **External Editor Support**: Open your preferred editor for composing messages
*   **Named Arguments for Custom Commands**: Create powerful custom commands with multiple named placeholders

Installation
------------

[](#installation)

### Using the Install Script

[](#using-the-install-script)

# Install the latest version
curl -fsSL https://raw.githubusercontent.com/opencode-ai/opencode/refs/heads/main/install | bash

# Install a specific version
curl -fsSL https://raw.githubusercontent.com/opencode-ai/opencode/refs/heads/main/install | VERSION=0.1.0 bash

### Using Homebrew (macOS and Linux)

[](#using-homebrew-macos-and-linux)

brew install opencode-ai/tap/opencode

### Using AUR (Arch Linux)

[](#using-aur-arch-linux)

# Using yay
yay -S opencode-ai-bin

# Using paru
paru -S opencode-ai-bin

### Using Go

[](#using-go)

go install github.com/opencode-ai/opencode@latest

Configuration
-------------

[](#configuration)

OpenCode looks for configuration in the following locations:

*   `$HOME/.opencode.json`
*   `$XDG_CONFIG_HOME/opencode/.opencode.json`
*   `./.opencode.json` (local directory)

### Auto Compact Feature

[](#auto-compact-feature)

OpenCode includes an auto compact feature that automatically summarizes your conversation when it approaches the model's context window limit. When enabled (default setting), this feature:

*   Monitors token usage during your conversation
*   Automatically triggers summarization when usage reaches 95% of the model's context window
*   Creates a new session with the summary, allowing you to continue your work without losing context
*   Helps prevent "out of context" errors that can occur with long conversations

You can enable or disable this feature in your configuration file:

{
  "autoCompact": true // default is true
}

### Environment Variables

[](#environment-variables)

You can configure OpenCode using environment variables:

Environment Variable

Purpose

`ANTHROPIC_API_KEY`

For Claude models

`OPENAI_API_KEY`

For OpenAI models

`GEMINI_API_KEY`

For Google Gemini models

`GITHUB_TOKEN`

For Github Copilot models (see [Using Github Copilot](#using-github-copilot))

`VERTEXAI_PROJECT`

For Google Cloud VertexAI (Gemini)

`VERTEXAI_LOCATION`

For Google Cloud VertexAI (Gemini)

`GROQ_API_KEY`

For Groq models

`AWS_ACCESS_KEY_ID`

For AWS Bedrock (Claude)

`AWS_SECRET_ACCESS_KEY`

For AWS Bedrock (Claude)

`AWS_REGION`

For AWS Bedrock (Claude)

`AZURE_OPENAI_ENDPOINT`

For Azure OpenAI models

`AZURE_OPENAI_API_KEY`

For Azure OpenAI models (optional when using Entra ID)

`AZURE_OPENAI_API_VERSION`

For Azure OpenAI models

`LOCAL_ENDPOINT`

For self-hosted models

`SHELL`

Default shell to use (if not specified in config)

### Shell Configuration

[](#shell-configuration)

OpenCode allows you to configure the shell used by the bash tool. By default, it uses the shell specified in the `SHELL` environment variable, or falls back to `/bin/bash` if not set.

You can override this in your configuration file:

{
  "shell": {
    "path": "/bin/zsh",
    "args": \["\-l"\]
  }
}

This is useful if you want to use a different shell than your default system shell, or if you need to pass specific arguments to the shell.

### Configuration File Structure

[](#configuration-file-structure)

{
  "data": {
    "directory": ".opencode"
  },
  "providers": {
    "openai": {
      "apiKey": "your-api-key",
      "disabled": false
    },
    "anthropic": {
      "apiKey": "your-api-key",
      "disabled": false
    },
    "copilot": {
      "disabled": false
    },
    "groq": {
      "apiKey": "your-api-key",
      "disabled": false
    },
    "openrouter": {
      "apiKey": "your-api-key",
      "disabled": false
    }
  },
  "agents": {
    "coder": {
      "model": "claude-3.7-sonnet",
      "maxTokens": 5000
    },
    "task": {
      "model": "claude-3.7-sonnet",
      "maxTokens": 5000
    },
    "title": {
      "model": "claude-3.7-sonnet",
      "maxTokens": 80
    }
  },
  "shell": {
    "path": "/bin/bash",
    "args": \["\-l"\]
  },
  "mcpServers": {
    "example": {
      "type": "stdio",
      "command": "path/to/mcp-server",
      "env": \[\],
      "args": \[\]
    }
  },
  "lsp": {
    "go": {
      "disabled": false,
      "command": "gopls"
    }
  },
  "debug": false,
  "debugLSP": false,
  "autoCompact": true
}

Supported AI Models
-------------------

[](#supported-ai-models)

OpenCode supports a variety of AI models from different providers:

### OpenAI

[](#openai)

*   GPT-4.1 family (gpt-4.1, gpt-4.1-mini, gpt-4.1-nano)
*   GPT-4.5 Preview
*   GPT-4o family (gpt-4o, gpt-4o-mini)
*   O1 family (o1, o1-pro, o1-mini)
*   O3 family (o3, o3-mini)
*   O4 Mini

### Anthropic

[](#anthropic)

*   Claude 4 Sonnet
*   Claude 4 Opus
*   Claude 3.5 Sonnet
*   Claude 3.5 Haiku
*   Claude 3.7 Sonnet
*   Claude 3 Haiku
*   Claude 3 Opus

### GitHub Copilot

[](#github-copilot)

*   GPT-3.5 Turbo
*   GPT-4
*   GPT-4o
*   GPT-4o Mini
*   GPT-4.1
*   Claude 3.5 Sonnet
*   Claude 3.7 Sonnet
*   Claude 3.7 Sonnet Thinking
*   Claude Sonnet 4
*   O1
*   O3 Mini
*   O4 Mini
*   Gemini 2.0 Flash
*   Gemini 2.5 Pro

### Google

[](#google)

*   Gemini 2.5
*   Gemini 2.5 Flash
*   Gemini 2.0 Flash
*   Gemini 2.0 Flash Lite

### AWS Bedrock

[](#aws-bedrock)

*   Claude 3.7 Sonnet

### Groq

[](#groq)

*   Llama 4 Maverick (17b-128e-instruct)
*   Llama 4 Scout (17b-16e-instruct)
*   QWEN QWQ-32b
*   Deepseek R1 distill Llama 70b
*   Llama 3.3 70b Versatile

### Azure OpenAI

[](#azure-openai)

*   GPT-4.1 family (gpt-4.1, gpt-4.1-mini, gpt-4.1-nano)
*   GPT-4.5 Preview
*   GPT-4o family (gpt-4o, gpt-4o-mini)
*   O1 family (o1, o1-mini)
*   O3 family (o3, o3-mini)
*   O4 Mini

### Google Cloud VertexAI

[](#google-cloud-vertexai)

*   Gemini 2.5
*   Gemini 2.5 Flash

Usage
-----

[](#usage)

# Start OpenCode
opencode

# Start with debug logging
opencode -d

# Start with a specific working directory
opencode -c /path/to/project

Non-interactive Prompt Mode
---------------------------

[](#non-interactive-prompt-mode)

You can run OpenCode in non-interactive mode by passing a prompt directly as a command-line argument. This is useful for scripting, automation, or when you want a quick answer without launching the full TUI.

# Run a single prompt and print the AI's response to the terminal
opencode -p "Explain the use of context in Go"

# Get response in JSON format
opencode -p "Explain the use of context in Go" -f json

# Run without showing the spinner (useful for scripts)
opencode -p "Explain the use of context in Go" -q

In this mode, OpenCode will process your prompt, print the result to standard output, and then exit. All permissions are auto-approved for the session.

By default, a spinner animation is displayed while the model is processing your query. You can disable this spinner with the `-q` or `--quiet` flag, which is particularly useful when running OpenCode from scripts or automated workflows.

### Output Formats

[](#output-formats)

OpenCode supports the following output formats in non-interactive mode:

Format

Description

`text`

Plain text output (default)

`json`

Output wrapped in a JSON object

The output format is implemented as a strongly-typed `OutputFormat` in the codebase, ensuring type safety and validation when processing outputs.

Command-line Flags
------------------

[](#command-line-flags)

Flag

Short

Description

`--help`

`-h`

Display help information

`--debug`

`-d`

Enable debug mode

`--cwd`

`-c`

Set current working directory

`--prompt`

`-p`

Run a single prompt in non-interactive mode

`--output-format`

`-f`

Output format for non-interactive mode (text, json)

`--quiet`

`-q`

Hide spinner in non-interactive mode

Keyboard Shortcuts
------------------

[](#keyboard-shortcuts)

### Global Shortcuts

[](#global-shortcuts)

Shortcut

Action

`Ctrl+C`

Quit application

`Ctrl+?`

Toggle help dialog

`?`

Toggle help dialog (when not in editing mode)

`Ctrl+L`

View logs

`Ctrl+A`

Switch session

`Ctrl+K`

Command dialog

`Ctrl+O`

Toggle model selection dialog

`Esc`

Close current overlay/dialog or return to previous mode

### Chat Page Shortcuts

[](#chat-page-shortcuts)

Shortcut

Action

`Ctrl+N`

Create new session

`Ctrl+X`

Cancel current operation/generation

`i`

Focus editor (when not in writing mode)

`Esc`

Exit writing mode and focus messages

### Editor Shortcuts

[](#editor-shortcuts)

Shortcut

Action

`Ctrl+S`

Send message (when editor is focused)

`Enter` or `Ctrl+S`

Send message (when editor is not focused)

`Ctrl+E`

Open external editor

`Esc`

Blur editor and focus messages

### Session Dialog Shortcuts

[](#session-dialog-shortcuts)

Shortcut

Action

`↑` or `k`

Previous session

`↓` or `j`

Next session

`Enter`

Select session

`Esc`

Close dialog

### Model Dialog Shortcuts

[](#model-dialog-shortcuts)

Shortcut

Action

`↑` or `k`

Move up

`↓` or `j`

Move down

`←` or `h`

Previous provider

`→` or `l`

Next provider

`Esc`

Close dialog

### Permission Dialog Shortcuts

[](#permission-dialog-shortcuts)

Shortcut

Action

`←` or `left`

Switch options left

`→` or `right` or `tab`

Switch options right

`Enter` or `space`

Confirm selection

`a`

Allow permission

`A`

Allow permission for session

`d`

Deny permission

### Logs Page Shortcuts

[](#logs-page-shortcuts)

Shortcut

Action

`Backspace` or `q`

Return to chat page

AI Assistant Tools
------------------

[](#ai-assistant-tools)

OpenCode's AI assistant has access to various tools to help with coding tasks:

### File and Code Tools

[](#file-and-code-tools)

Tool

Description

Parameters

`glob`

Find files by pattern

`pattern` (required), `path` (optional)

`grep`

Search file contents

`pattern` (required), `path` (optional), `include` (optional), `literal_text` (optional)

`ls`

List directory contents

`path` (optional), `ignore` (optional array of patterns)

`view`

View file contents

`file_path` (required), `offset` (optional), `limit` (optional)

`write`

Write to files

`file_path` (required), `content` (required)

`edit`

Edit files

Various parameters for file editing

`patch`

Apply patches to files

`file_path` (required), `diff` (required)

`diagnostics`

Get diagnostics information

`file_path` (optional)

### Other Tools

[](#other-tools)

Tool

Description

Parameters

`bash`

Execute shell commands

`command` (required), `timeout` (optional)

`fetch`

Fetch data from URLs

`url` (required), `format` (required), `timeout` (optional)

`sourcegraph`

Search code across public repositories

`query` (required), `count` (optional), `context_window` (optional), `timeout` (optional)

`agent`

Run sub-tasks with the AI agent

`prompt` (required)

Architecture
------------

[](#architecture)

OpenCode is built with a modular architecture:

*   **cmd**: Command-line interface using Cobra
*   **internal/app**: Core application services
*   **internal/config**: Configuration management
*   **internal/db**: Database operations and migrations
*   **internal/llm**: LLM providers and tools integration
*   **internal/tui**: Terminal UI components and layouts
*   **internal/logging**: Logging infrastructure
*   **internal/message**: Message handling
*   **internal/session**: Session management
*   **internal/lsp**: Language Server Protocol integration

Custom Commands
---------------

[](#custom-commands)

OpenCode supports custom commands that can be created by users to quickly send predefined prompts to the AI assistant.

### Creating Custom Commands

[](#creating-custom-commands)

Custom commands are predefined prompts stored as Markdown files in one of three locations:

1.  **User Commands** (prefixed with `user:`):
    
        $XDG_CONFIG_HOME/opencode/commands/
        
    
    (typically `~/.config/opencode/commands/` on Linux/macOS)
    
    or
    
        $HOME/.opencode/commands/
        
    
2.  **Project Commands** (prefixed with `project:`):
    
        <PROJECT DIR>/.opencode/commands/
        
    

Each `.md` file in these directories becomes a custom command. The file name (without extension) becomes the command ID.

For example, creating a file at `~/.config/opencode/commands/prime-context.md` with content:

RUN git ls-files
READ README.md

This creates a command called `user:prime-context`.

### Command Arguments

[](#command-arguments)

OpenCode supports named arguments in custom commands using placeholders in the format `$NAME` (where NAME consists of uppercase letters, numbers, and underscores, and must start with a letter).

For example:

\# Fetch Context for Issue $ISSUE\_NUMBER

RUN gh issue view $ISSUE\_NUMBER --json title,body,comments
RUN git grep --author="$AUTHOR\_NAME" -n .
RUN grep -R "$SEARCH\_PATTERN" $DIRECTORY

When you run a command with arguments, OpenCode will prompt you to enter values for each unique placeholder. Named arguments provide several benefits:

*   Clear identification of what each argument represents
*   Ability to use the same argument multiple times
*   Better organization for commands with multiple inputs

### Organizing Commands

[](#organizing-commands)

You can organize commands in subdirectories:

    ~/.config/opencode/commands/git/commit.md
    

This creates a command with ID `user:git:commit`.

### Using Custom Commands

[](#using-custom-commands)

1.  Press `Ctrl+K` to open the command dialog
2.  Select your custom command (prefixed with either `user:` or `project:`)
3.  Press Enter to execute the command

The content of the command file will be sent as a message to the AI assistant.

### Built-in Commands

[](#built-in-commands)

OpenCode includes several built-in commands:

Command

Description

Initialize Project

Creates or updates the OpenCode.md memory file with project-specific information

Compact Session

Manually triggers the summarization of the current session, creating a new session with the summary

MCP (Model Context Protocol)
----------------------------

[](#mcp-model-context-protocol)

OpenCode implements the Model Context Protocol (MCP) to extend its capabilities through external tools. MCP provides a standardized way for the AI assistant to interact with external services and tools.

### MCP Features

[](#mcp-features)

*   **External Tool Integration**: Connect to external tools and services via a standardized protocol
*   **Tool Discovery**: Automatically discover available tools from MCP servers
*   **Multiple Connection Types**:
    *   **Stdio**: Communicate with tools via standard input/output
    *   **SSE**: Communicate with tools via Server-Sent Events
*   **Security**: Permission system for controlling access to MCP tools

### Configuring MCP Servers

[](#configuring-mcp-servers)

MCP servers are defined in the configuration file under the `mcpServers` section:

{
  "mcpServers": {
    "example": {
      "type": "stdio",
      "command": "path/to/mcp-server",
      "env": \[\],
      "args": \[\]
    },
    "web-example": {
      "type": "sse",
      "url": "https://example.com/mcp",
      "headers": {
        "Authorization": "Bearer token"
      }
    }
  }
}

### MCP Tool Usage

[](#mcp-tool-usage)

Once configured, MCP tools are automatically available to the AI assistant alongside built-in tools. They follow the same permission model as other tools, requiring user approval before execution.

LSP (Language Server Protocol)
------------------------------

[](#lsp-language-server-protocol)

OpenCode integrates with Language Server Protocol to provide code intelligence features across multiple programming languages.

### LSP Features

[](#lsp-features)

*   **Multi-language Support**: Connect to language servers for different programming languages
*   **Diagnostics**: Receive error checking and linting information
*   **File Watching**: Automatically notify language servers of file changes

### Configuring LSP

[](#configuring-lsp)

Language servers are configured in the configuration file under the `lsp` section:

{
  "lsp": {
    "go": {
      "disabled": false,
      "command": "gopls"
    },
    "typescript": {
      "disabled": false,
      "command": "typescript-language-server",
      "args": \["\--stdio"\]
    }
  }
}

### LSP Integration with AI

[](#lsp-integration-with-ai)

The AI assistant can access LSP features through the `diagnostics` tool, allowing it to:

*   Check for errors in your code
*   Suggest fixes based on diagnostics

While the LSP client implementation supports the full LSP protocol (including completions, hover, definition, etc.), currently only diagnostics are exposed to the AI assistant.

Using Github Copilot
--------------------

[](#using-github-copilot)

_Copilot support is currently experimental._

### Requirements

[](#requirements)

*   [Copilot chat in the IDE](https://github.com/settings/copilot) enabled in GitHub settings
*   One of:
    *   VSCode Github Copilot chat extension
    *   Github `gh` CLI
    *   Neovim Github Copilot plugin (`copilot.vim` or `copilot.lua`)
    *   Github token with copilot permissions

If using one of the above plugins or cli tools, make sure you use the authenticate the tool with your github account. This should create a github token at one of the following locations:

*   ~/.config/github-copilot/\[hosts,apps\].json
*   $XDG\_CONFIG\_HOME/github-copilot/\[hosts,apps\].json

If using an explicit github token, you may either set the $GITHUB\_TOKEN environment variable or add it to the opencode.json config file at `providers.copilot.apiKey`.

Using a self-hosted model provider
----------------------------------

[](#using-a-self-hosted-model-provider)

OpenCode can also load and use models from a self-hosted (OpenAI-like) provider. This is useful for developers who want to experiment with custom models.

### Configuring a self-hosted provider

[](#configuring-a-self-hosted-provider)

You can use a self-hosted model by setting the `LOCAL_ENDPOINT` environment variable. This will cause OpenCode to load and use the models from the specified endpoint.

LOCAL\_ENDPOINT=http://localhost:1235/v1

### Configuring a self-hosted model

[](#configuring-a-self-hosted-model)

You can also configure a self-hosted model in the configuration file under the `agents` section:

{
  "agents": {
    "coder": {
      "model": "local.granite-3.3-2b-instruct@q8\_0",
      "reasoningEffort": "high"
    }
  }
}

Development
-----------

[](#development)

### Prerequisites

[](#prerequisites)

*   Go 1.24.0 or higher

### Building from Source

[](#building-from-source)

# Clone the repository
git clone https://github.com/opencode-ai/opencode.git
cd opencode

# Build
go build -o opencode

# Run
./opencode

Acknowledgments
---------------

[](#acknowledgments)

OpenCode gratefully acknowledges the contributions and support from these key individuals:

*   [@isaacphi](https://github.com/isaacphi) - For the [mcp-language-server](https://github.com/isaacphi/mcp-language-server) project which provided the foundation for our LSP client implementation
*   [@adamdottv](https://github.com/adamdottv) - For the design direction and UI/UX architecture

Special thanks to the broader open source community whose tools and libraries have made this project possible.

License
-------

[](#license)

OpenCode is licensed under the MIT License. See the [LICENSE](/opencode-ai/opencode/blob/main/LICENSE) file for details.

Contributing
------------

[](#contributing)

Contributions are welcome! Here's how you can contribute:

1.  Fork the repository
2.  Create a feature branch (`git checkout -b feature/amazing-feature`)
3.  Commit your changes (`git commit -m 'Add some amazing feature'`)
4.  Push to the branch (`git push origin feature/amazing-feature`)
5.  Open a Pull Request

Please make sure to update tests as appropriate and follow the existing code style.

{"resolvedServerColorMode":"day"}

About
-----

A powerful AI coding agent. Built for the terminal.

### Topics

[ai](/topics/ai "Topic: ai") [code](/topics/code "Topic: code") [openai](/topics/openai "Topic: openai") [claude](/topics/claude "Topic: claude") [llm](/topics/llm "Topic: llm")

### Resources

[Readme](#readme-ov-file)

### License

[MIT license](#MIT-1-ov-file)

### Uh oh!

There was an error while loading. Please reload this page.

[Activity](/opencode-ai/opencode/activity)

[Custom properties](/opencode-ai/opencode/custom-properties)

### Stars

[**6.7k** stars](/opencode-ai/opencode/stargazers)

### Watchers

[**44** watching](/opencode-ai/opencode/watchers)

### Forks

[**499** forks](/opencode-ai/opencode/forks)

[Report repository](/contact/report-content?content_url=https%3A%2F%2Fgithub.com%2Fopencode-ai%2Fopencode&report=opencode-ai+%28user%29)

[Releases 50](/opencode-ai/opencode/releases)
---------------------------------------------

[

v0.0.55 Latest

Jun 27, 2025



](/opencode-ai/opencode/releases/tag/v0.0.55)

[\+ 49 releases](/opencode-ai/opencode/releases)

[Packages 0](/orgs/opencode-ai/packages?repo_name=opencode)
-----------------------------------------------------------

No packages published  

### Uh oh!

There was an error while loading. Please reload this page.

[Contributors 27](/opencode-ai/opencode/graphs/contributors)
------------------------------------------------------------

*   [![@kujtimiihoxha](https://avatars.githubusercontent.com/u/14311743?s=64&v=4)](https://github.com/kujtimiihoxha)
*   [![@ezynda3](https://avatars.githubusercontent.com/u/5308871?s=64&v=4)](https://github.com/ezynda3)
*   [![@rekram1-node](https://avatars.githubusercontent.com/u/63023139?s=64&v=4)](https://github.com/rekram1-node)
*   [![@AvicennaJr](https://avatars.githubusercontent.com/u/87450618?s=64&v=4)](https://github.com/AvicennaJr)
*   [![@garrettladley](https://avatars.githubusercontent.com/u/92384606?s=64&v=4)](https://github.com/garrettladley)
*   [![@jdlms](https://avatars.githubusercontent.com/u/97018563?s=64&v=4)](https://github.com/jdlms)
*   [![@Pietjan](https://avatars.githubusercontent.com/u/962728?s=64&v=4)](https://github.com/Pietjan)
*   [![@NachoVazquez](https://avatars.githubusercontent.com/u/9338604?s=64&v=4)](https://github.com/NachoVazquez)
*   [![@isaac-scarrott](https://avatars.githubusercontent.com/u/20249678?s=64&v=4)](https://github.com/isaac-scarrott)
*   [![@enchantednatures](https://avatars.githubusercontent.com/u/41604962?s=64&v=4)](https://github.com/enchantednatures)
*   [![@EkeMinusYou](https://avatars.githubusercontent.com/u/41781157?s=64&v=4)](https://github.com/EkeMinusYou)
*   [![@meowgorithm](https://avatars.githubusercontent.com/u/25087?s=64&v=4)](https://github.com/meowgorithm)
*   [![@radutopala](https://avatars.githubusercontent.com/u/647137?s=64&v=4)](https://github.com/radutopala)
*   [![@aldehir](https://avatars.githubusercontent.com/u/765227?s=64&v=4)](https://github.com/aldehir)

[\+ 13 contributors](/opencode-ai/opencode/graphs/contributors)

Languages
---------

*   [Go 99.2%](/opencode-ai/opencode/search?l=go)
*   [Shell 0.8%](/opencode-ai/opencode/search?l=shell)

Footer
------

[](https://github.com)© 2025 GitHub, Inc.

### Footer navigation

*   [Terms](https://docs.github.com/site-policy/github-terms/github-terms-of-service)
*   [Privacy](https://docs.github.com/site-policy/privacy-policies/github-privacy-statement)
*   [Security](https://github.com/security)
*   [Status](https://www.githubstatus.com/)
*   [Docs](https://docs.github.com/)
*   [Contact](https://support.github.com?tags=dotcom-footer)
*   Manage cookies
*   Do not share my personal information

You can’t perform that action at this time.