Scraped content of https://www.npmjs.com/package/poly:

  poly - npm  

skip to:[content](#main)[package search](#search)[sign in](#signin)

❤

*   [Pro](/products/pro)
*   [Teams](/products/teams)
*   [Pricing](/products)
*   [Documentation](https://docs.npmjs.com)

npm

[](/)

Search

[Sign Up](/signup)[Sign In](/login)

poly

![TypeScript icon, indicating that this package has built-in type declarations](https://static-production.npmjs.com/255a118f56f5346b97e56325a1217a16.svg "This package contains built-in TypeScript declarations")


--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

0.7.2 • Public • Published 5 months ago

*   [Readme](?activeTab=readme)
*   [Code Beta](?activeTab=code)
*   [14 Dependencies](?activeTab=dependencies)
*   [1 Dependents](?activeTab=dependents)
*   [4 Versions](?activeTab=versions)

Poly
====

[](#poly)

 [![](https://camo.githubusercontent.com/8b605293dcc74afb8290eba4166d77e4311d0c387d264cada3f5f621f0183125/68747470733a2f2f696d672e736869656c64732e696f2f62616467652f6e706d253230696e7374616c6c253230706f6c792d3465393035373f7374796c653d666f722d7468652d6261646765)](https://npmjs.com/poly)  [![](https://camo.githubusercontent.com/e380eda1d738e294848589c9f5f14935fa5d988706410ac0e9d66d521a00b82f/68747470733a2f2f696d672e736869656c64732e696f2f646973636f72642f313235363539303331323137373031323830363f7374796c653d666f722d7468652d6261646765266c6162656c3d63686174266c6f676f436f6c6f723d66666626636f6c6f723d353836356632)](https://discord.gg/RSg4NNwM4f)

  

What is Poly?
-------------

[](#what-is-poly)

Poly is a Svelte 4 fork with the goal of long-term support. For now, we will focus on technical improvements with no API changes, but we may add additional incremental features in the future. It is not planned to deprecate any Svelte 4 APIs at this point in time, and, depending on adoption, they may never be deprecated.

### Svelte Compatibility

[](#svelte-compatibility)

Poly is fully compatible with Svelte 4. In particular existing code, including code that uses `<svelte:...>` tags, should work flawlessly. Please file an issue if your existing Svelte 4 code doesn't work in Poly.

### What about SvelteKit?

[](#what-about-sveltekit)

If you're a user of SvelteKit, you will need to migrate your app to Primate, which is a full-stack framework that supports a multitude of backends and frontends, including Svelte. Support for Poly will be added in the coming days.

License
-------

[](#license)

MIT

Contributing
------------

[](#contributing)

By contributing to Poly, you agree that your contributions will be licensed under its MIT license.

Readme
------

### Keywords

none

Package Sidebar
---------------

### Install

`npm i poly`

### Repository

[Gitgithub.com/terrablue/poly](https://github.com/terrablue/poly)

### Homepage

[github.com/terrablue/poly#readme](https://github.com/terrablue/poly#readme)

### DownloadsWeekly Downloads

29

### Version

0.7.2

### License

MIT

### Unpacked Size

2.76 MB

### Total Files

251

### Last publish

5 months ago

### Collaborators

*   [![terrablue](/npm-avatar/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdmF0YXJVUkwiOiJodHRwczovL3MuZ3JhdmF0YXIuY29tL2F2YXRhci83M2YxYzBiYWJjZDJlMjA2NDQ2OTExNjA2ZWNhNzg3MT9zaXplPTEwMCZkZWZhdWx0PXJldHJvIn0.CL-RTnXBqKQskk-S-Wgpcfczfi050LQvaGWDRqiib-Y "terrablue")](/~terrablue)
    

[**Try** on RunKit](https://runkit.com/npm/poly)

[**Report** malware](/support?inquire=security&security-inquire=malware&package=poly&version=0.7.2)

Footer
------

[](https://github.com/npm)

[](https://github.com)

### Support

*   [Help](https://docs.npmjs.com)
*   [Advisories](https://github.com/advisories)
*   [Status](http://status.npmjs.org/)
*   [Contact npm](/support)

### Company

*   [About](/about)
*   [Blog](https://github.blog/tag/npm/)
*   [Press](/press)

### Terms & Policies

*   [Policies](/policies/)
*   [Terms of Use](/policies/terms)
*   [Code of Conduct](/policies/conduct)
*   [Privacy](/policies/privacy)

window.\_\_context\_\_ = {"context":{"notifications":\[\],"csrftoken":"5EFfZz8rn2l6CG0fvK1oZcJDGddowxlRgaWaFP2ka6U","userEmailVerified":null,"auditLogEnabled":false,"user":null,"documentContext":{"readme.data":"readme"},"undefined":true,"readme":{"ref":"readme","data":null},"versionsDownloads":{"0.6.1":6,"0.7.0":2,"0.7.1":2,"0.7.2":19},"starAction":"/package/poly/star","provenance":{"enabled":false,"feedbackUrl":" https://github.com/npm/feedback"},"isSecurityPlaceholder":false,"private":false,"devDeps":\["@playwright/test","@rollup/plugin-commonjs","@rollup/plugin-json","@rollup/plugin-node-resolve","@types/aria-query","agadoo","dts-buddy","esbuild","happy-dom","jsdom","kleur","playwright","rollup","source-map","tiny-glob","typescript","vitest"\],"deps":\["@ampproject/remapping","@jridgewell/sourcemap-codec","@jridgewell/trace-mapping","@types/estree","acorn","aria-query","axobject-query","code-red","css-tree","estree-walker","is-reference","locate-character","magic-string","periscopic"\],"packument":{"description":"Svelte 4 LTS fork","homepage":"https://github.com/terrablue/poly#readme","repository":"https://github.com/terrablue/poly","distTags":{"latest":"0.7.2"},"maintainers":\[{"name":"terrablue","avatars":{"small":"/npm-avatar/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdmF0YXJVUkwiOiJodHRwczovL3MuZ3JhdmF0YXIuY29tL2F2YXRhci83M2YxYzBiYWJjZDJlMjA2NDQ2OTExNjA2ZWNhNzg3MT9zaXplPTUwJmRlZmF1bHQ9cmV0cm8ifQ.Ipz6rKDf5mUnQ1Cwb8InWCMFFi8jjeoM7TFNdvR49S8","medium":"/npm-avatar/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdmF0YXJVUkwiOiJodHRwczovL3MuZ3JhdmF0YXIuY29tL2F2YXRhci83M2YxYzBiYWJjZDJlMjA2NDQ2OTExNjA2ZWNhNzg3MT9zaXplPTEwMCZkZWZhdWx0PXJldHJvIn0.CL-RTnXBqKQskk-S-Wgpcfczfi050LQvaGWDRqiib-Y","large":"/npm-avatar/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdmF0YXJVUkwiOiJodHRwczovL3MuZ3JhdmF0YXIuY29tL2F2YXRhci83M2YxYzBiYWJjZDJlMjA2NDQ2OTExNjA2ZWNhNzg3MT9zaXplPTQ5NiZkZWZhdWx0PXJldHJvIn0.IiV7wF4lVRJJCF-U8No2Mzg6op034rcnOvrDU1fz\_nA"}}\],"name":"poly","license":"MIT","version":"0.7.2","versions":\[{"version":"0.7.2","date":{"ts":1737561481341,"rel":"5 months ago"},"dist":{"integrity":"sha512-g+3oBXwZxs58QMcala9R6x13+pDOPEMJRTvtmcULwJW3pzvy+EY+IwDgsj3KaIJHCw0XYzBeeHMQ7DgvMlmZ1A==","shasum":"faca3c62eadb0cf874170905e8b95dc0825f3e22","tarball":"https://registry.npmjs.org/poly/-/poly-0.7.2.tgz","fileCount":251,"unpackedSize":2762635,"signatures":\[{"keyid":"SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U","sig":"MEQCICACSq5gqSFcwgx4wG8nbLfY/9W3FOkszILXNjXHN6aoAiA0HsVAImSa9/O5TgJffaoIRgIntquEjQobqtWCtbBK4A=="}\]}},{"version":"0.7.1","date":{"ts":1737298359079,"rel":"5 months ago"},"dist":{"shasum":"126c13939235d1f78057ba056a4495379b303592","tarball":"https://registry.npmjs.org/poly/-/poly-0.7.1.tgz","fileCount":249,"integrity":"sha512-Z8TNSsCe9ph8Xx+Y3PNqtHTntJD9T3mcHbs+v17rvzgL5KXUyz1W9TUWr9Y/ABeElDV4z7STBJSYYnckYuxafA==","signatures":\[{"sig":"MEUCIAUlkMafaaIpd1gpJep96aGtC/Ec8uP5CfeH1aGxPLxVAiEAin1Wu4gUr6r13Jt8o9u9XK7BBFzjLXwjnFwe0hxEf74=","keyid":"SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}\],"unpackedSize":2760695}},{"version":"0.7.0","date":{"ts":1737296021630,"rel":"5 months ago"},"deprecated":"This version has been deprecated","dist":{"shasum":"ab0776fba5551217cd85c48f80aaa4ce387cf99b","tarball":"https://registry.npmjs.org/poly/-/poly-0.7.0.tgz","fileCount":250,"integrity":"sha512-weIq+sWEqWn7Z+IBQSPMAa4y/7hvSD59zb2dkx46TOU2hx9+SJ9JxPj6HQ+hjA+LjQyUjehEA2RqIiUdrw7p/g==","signatures":\[{"sig":"MEUCIG71+QTUg/g7QSTeT1HoOGcmRtN+cIPcK3Iv/FgKh4uEAiEAljFNMA30RbCKHPi5CYFTkVgkrAomFG8nZPMIIjlQNWQ=","keyid":"SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}\],"unpackedSize":2761781}},{"version":"0.6.1","date":{"ts":1430226550532,"rel":"10 years ago"},"deprecated":"This version has been deprecated","dist":{"shasum":"36b462a053e8f208c4f4db612425bc02ac4faf77","tarball":"https://registry.npmjs.org/poly/-/poly-0.6.1.tgz","integrity":"sha512-bxxzyHPNhgCy+nac0pjBDHBA5p8eFpORSpnAvdk0sesSDxJ4zvGPt4CJxYzRUCHlixvT09AzzKbYGvYVpEedyw==","signatures":\[{"sig":"MEQCIH2qjMz/V1OwqB5GoT/N+Mm2m5IIuVxrhQjFktKt8R+NAiAM7e7SZeIzFRSH7koz2isgHxpFQn8K0YKIHmxOlLhOZg==","keyid":"SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}\]}}\],"deprecations":\["0.6.1","0.7.0"\]},"packageVersion":{"description":"Svelte 4 LTS fork","homepage":"https://github.com/terrablue/poly#readme","repository":"https://github.com/terrablue/poly","dependencies":{"@ampproject/remapping":"^2.3.0","@jridgewell/sourcemap-codec":"^1.5.0","@jridgewell/trace-mapping":"^0.3.25","@types/estree":"^1.0.6","acorn":"^8.14.0","aria-query":"^5.3.2","axobject-query":"^4.1.0","code-red":"^1.0.4","css-tree":"^2.3.1","estree-walker":"^3.0.3","is-reference":"^3.0.3","locate-character":"^3.0.0","magic-string":"^0.30.17","periscopic":"^3.1.0"},"devDependencies":{"@playwright/test":"^1.49.1","@rollup/plugin-commonjs":"^24.1.0","@rollup/plugin-json":"^6.1.0","@rollup/plugin-node-resolve":"^15.1.0","@types/aria-query":"^5.0.4","agadoo":"^3.0.0","dts-buddy":"^0.4.7","esbuild":"^0.24.2","happy-dom":"^15.10.8","jsdom":"22.0.0","kleur":"^4.1.5","playwright":"^1.49.1","rollup":"^3.26.2","source-map":"^0.7.4","tiny-glob":"^0.2.9","typescript":"~5.4.5","vitest":"^0.33.0"},"maintainers":\[{"name":"terrablue","avatars":{"small":"/npm-avatar/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdmF0YXJVUkwiOiJodHRwczovL3MuZ3JhdmF0YXIuY29tL2F2YXRhci83M2YxYzBiYWJjZDJlMjA2NDQ2OTExNjA2ZWNhNzg3MT9zaXplPTUwJmRlZmF1bHQ9cmV0cm8ifQ.Ipz6rKDf5mUnQ1Cwb8InWCMFFi8jjeoM7TFNdvR49S8","medium":"/npm-avatar/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdmF0YXJVUkwiOiJodHRwczovL3MuZ3JhdmF0YXIuY29tL2F2YXRhci83M2YxYzBiYWJjZDJlMjA2NDQ2OTExNjA2ZWNhNzg3MT9zaXplPTEwMCZkZWZhdWx0PXJldHJvIn0.CL-RTnXBqKQskk-S-Wgpcfczfi050LQvaGWDRqiib-Y","large":"/npm-avatar/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdmF0YXJVUkwiOiJodHRwczovL3MuZ3JhdmF0YXIuY29tL2F2YXRhci83M2YxYzBiYWJjZDJlMjA2NDQ2OTExNjA2ZWNhNzg3MT9zaXplPTQ5NiZkZWZhdWx0PXJldHJvIn0.IiV7wF4lVRJJCF-U8No2Mzg6op034rcnOvrDU1fz\_nA"}}\],"name":"poly","license":"MIT","version":"0.7.2","versions":\[\],"deprecations":\[\],"types":"types/index.d.ts"},"packageUrl":"/package/poly","packageLinkingCallToActionHref":null,"package":"poly","linkingAllowedForPackage":false,"isStarred":false,"ghapi":"https://api.github.com/repos/terrablue/poly","downloads":\[{"downloads":25,"label":"2024-07-05 to 2024-07-11"},{"downloads":16,"label":"2024-07-12 to 2024-07-18"},{"downloads":13,"label":"2024-07-19 to 2024-07-25"},{"downloads":31,"label":"2024-07-26 to 2024-08-01"},{"downloads":15,"label":"2024-08-02 to 2024-08-08"},{"downloads":42,"label":"2024-08-09 to 2024-08-15"},{"downloads":7,"label":"2024-08-16 to 2024-08-22"},{"downloads":15,"label":"2024-08-23 to 2024-08-29"},{"downloads":31,"label":"2024-08-30 to 2024-09-05"},{"downloads":16,"label":"2024-09-06 to 2024-09-12"},{"downloads":6,"label":"2024-09-13 to 2024-09-19"},{"downloads":12,"label":"2024-09-20 to 2024-09-26"},{"downloads":11,"label":"2024-09-27 to 2024-10-03"},{"downloads":9,"label":"2024-10-04 to 2024-10-10"},{"downloads":10,"label":"2024-10-11 to 2024-10-17"},{"downloads":22,"label":"2024-10-18 to 2024-10-24"},{"downloads":45,"label":"2024-10-25 to 2024-10-31"},{"downloads":12,"label":"2024-11-01 to 2024-11-07"},{"downloads":15,"label":"2024-11-08 to 2024-11-14"},{"downloads":18,"label":"2024-11-15 to 2024-11-21"},{"downloads":36,"label":"2024-11-22 to 2024-11-28"},{"downloads":19,"label":"2024-11-29 to 2024-12-05"},{"downloads":6,"label":"2024-12-06 to 2024-12-12"},{"downloads":34,"label":"2024-12-13 to 2024-12-19"},{"downloads":10,"label":"2024-12-20 to 2024-12-26"},{"downloads":20,"label":"2024-12-27 to 2025-01-02"},{"downloads":9,"label":"2025-01-03 to 2025-01-09"},{"downloads":16,"label":"2025-01-10 to 2025-01-16"},{"downloads":239,"label":"2025-01-17 to 2025-01-23"},{"downloads":51,"label":"2025-01-24 to 2025-01-30"},{"downloads":12,"label":"2025-01-31 to 2025-02-06"},{"downloads":26,"label":"2025-02-07 to 2025-02-13"},{"downloads":11,"label":"2025-02-14 to 2025-02-20"},{"downloads":17,"label":"2025-02-21 to 2025-02-27"},{"downloads":14,"label":"2025-02-28 to 2025-03-06"},{"downloads":147,"label":"2025-03-07 to 2025-03-13"},{"downloads":78,"label":"2025-03-14 to 2025-03-20"},{"downloads":14,"label":"2025-03-21 to 2025-03-27"},{"downloads":17,"label":"2025-03-28 to 2025-04-03"},{"downloads":21,"label":"2025-04-04 to 2025-04-10"},{"downloads":10,"label":"2025-04-11 to 2025-04-17"},{"downloads":23,"label":"2025-04-18 to 2025-04-24"},{"downloads":26,"label":"2025-04-25 to 2025-05-01"},{"downloads":31,"label":"2025-05-02 to 2025-05-08"},{"downloads":22,"label":"2025-05-09 to 2025-05-15"},{"downloads":14,"label":"2025-05-16 to 2025-05-22"},{"downloads":8,"label":"2025-05-23 to 2025-05-29"},{"downloads":12,"label":"2025-05-30 to 2025-06-05"},{"downloads":26,"label":"2025-06-06 to 2025-06-12"},{"downloads":55,"label":"2025-06-13 to 2025-06-19"},{"downloads":30,"label":"2025-06-20 to 2025-06-26"},{"downloads":29,"label":"2025-06-27 to 2025-07-03"}\],"dependents":{"dependentsCount":"1","dependentsTruncated":\["localhandler","when"\]},"capsule":{"name":"poly","description":"Svelte 4 LTS fork","maintainers":\["terrablue"\],"dist-tags":{"latest":"0.7.2"},"lastPublish":{"maintainer":"terrablue","time":"2025-01-22T15:58:01.341Z"},"types":{"typescript":{"bundled":"types/index.d.ts"}}},"canEditPackage":false},"chunks":{"commons":\["commons.864e8e8e5aa9c8980866.js","commons.864e8e8e5aa9c8980866.js.map"\],"styles":\["styles.60bfb8445a405788b47e.css","minicssextractbug.67b6239bfe3438bfe328.js","styles.60bfb8445a405788b47e.css.map","minicssextractbug.67b6239bfe3438bfe328.js.map"\],"acceptance/acceptance":\["acceptance/acceptance.ee53d815bed42c72b1e1.js","acceptance/acceptance.ee53d815bed42c72b1e1.js.map"\],"audit-logs/actions/common/auditLogContainer":\["audit-logs/actions/common/auditLogContainer.9ca75b2627668a841eb9.js","audit-logs/actions/common/auditLogContainer.9ca75b2627668a841eb9.js.map"\],"audit-logs/actions/common/dot":\["audit-logs/actions/common/dot.0c68497a7bbb43ca7150.js","audit-logs/actions/common/dot.0c68497a7bbb43ca7150.js.map"\],"audit-logs/actions/common/filterActions":\["audit-logs/actions/common/filterActions.10690b8013721a202a2b.js","audit-logs/actions/common/filterActions.10690b8013721a202a2b.js.map"\],"audit-logs/actions/common/spaceGap":\["audit-logs/actions/common/spaceGap.aa2921bf2a5d6e05483a.js","audit-logs/actions/common/spaceGap.aa2921bf2a5d6e05483a.js.map"\],"audit-logs/actions/orgAddUser":\["audit-logs/actions/orgAddUser.8b3dcc33db93bdf69db5.js","audit-logs/actions/orgAddUser.8b3dcc33db93bdf69db5.js.map"\],"audit-logs/actions/orgCreateTeam":\["audit-logs/actions/orgCreateTeam.b652046797afffeeda33.js","audit-logs/actions/orgCreateTeam.b652046797afffeeda33.js.map"\],"audit-logs/actions/orgDeleteTeam":\["audit-logs/actions/orgDeleteTeam.944962e922d213913004.js","audit-logs/actions/orgDeleteTeam.944962e922d213913004.js.map"\],"audit-logs/actions/orgRemoveUser":\["audit-logs/actions/orgRemoveUser.d8ca6fb2d78773038de1.js","audit-logs/actions/orgRemoveUser.d8ca6fb2d78773038de1.js.map"\],"audit-logs/actions/packageAddDistTag":\["audit-logs/actions/packageAddDistTag.bce3bcc2e4b9b80a156d.js","audit-logs/actions/packageAddDistTag.bce3bcc2e4b9b80a156d.js.map"\],"audit-logs/actions/packageDeprecate":\["audit-logs/actions/packageDeprecate.f80626c7422abb85c08a.js","audit-logs/actions/packageDeprecate.f80626c7422abb85c08a.js.map"\],"audit-logs/actions/packagePublish":\["audit-logs/actions/packagePublish.63cf6c3433485e916fcd.js","audit-logs/actions/packagePublish.63cf6c3433485e916fcd.js.map"\],"audit-logs/actions/packageRemoveDistTag":\["audit-logs/actions/packageRemoveDistTag.ce8e99eeba8927f76f16.js","audit-logs/actions/packageRemoveDistTag.ce8e99eeba8927f76f16.js.map"\],"audit-logs/actions/packageUnpublish":\["audit-logs/actions/packageUnpublish.a1154e0916a4a48ac8a0.js","audit-logs/actions/packageUnpublish.a1154e0916a4a48ac8a0.js.map"\],"audit-logs/actions/packageUpdateAccess":\["audit-logs/actions/packageUpdateAccess.7211d161e692a85c579f.js","audit-logs/actions/packageUpdateAccess.7211d161e692a85c579f.js.map"\],"audit-logs/actions/teamAddPackage":\["audit-logs/actions/teamAddPackage.0d662bb2056a86b91fd4.js","audit-logs/actions/teamAddPackage.0d662bb2056a86b91fd4.js.map"\],"audit-logs/actions/teamAddUser":\["audit-logs/actions/teamAddUser.5cc465dd6c035567a7ab.js","audit-logs/actions/teamAddUser.5cc465dd6c035567a7ab.js.map"\],"audit-logs/actions/teamRemovePackage":\["audit-logs/actions/teamRemovePackage.5e3901cef0693f8b6c25.js","audit-logs/actions/teamRemovePackage.5e3901cef0693f8b6c25.js.map"\],"audit-logs/actions/teamRemoveUser":\["audit-logs/actions/teamRemoveUser.ed58d72a12cdbef77e5c.js","audit-logs/actions/teamRemoveUser.ed58d72a12cdbef77e5c.js.map"\],"audit-logs/actions/teamUpdatePackageAccess":\["audit-logs/actions/teamUpdatePackageAccess.38bc200f211d1a93aa93.js","audit-logs/actions/teamUpdatePackageAccess.38bc200f211d1a93aa93.js.map"\],"audit-logs/audit-logs":\["audit-logs/audit-logs.1359e2c87937cabb7ccd.js","audit-logs/audit-logs.1359e2c87937cabb7ccd.js.map"\],"audit-logs/audit-search-input":\["audit-logs/audit-search-input.d64b39cb0da8f4cf006f.js","audit-logs/audit-search-input.d64b39cb0da8f4cf006f.js.map"\],"audit-logs/list":\["audit-logs/list.ac2c470bc5a6b61ea9a9.js","audit-logs/list.ac2c470bc5a6b61ea9a9.js.map"\],"audit-logs/no-logs":\["audit-logs/no-logs.ea727f9879e77be2b177.js","audit-logs/no-logs.ea727f9879e77be2b177.js.map"\],"audit-logs/no-logs-with-query":\["audit-logs/no-logs-with-query.37fc4f2632219287c037.js","audit-logs/no-logs-with-query.37fc4f2632219287c037.js.map"\],"auth/account-recovery-message":\["auth/account-recovery-message.0e4aab3d442da425fe3a.js","auth/account-recovery-message.0e4aab3d442da425fe3a.js.map"\],"auth/authentication-successful":\["auth/authentication-successful.cb4ba1e5eea537be45bf.js","auth/authentication-successful.cb4ba1e5eea537be45bf.js.map"\],"auth/email-otp":\["auth/email-otp.bd2609b21d97d9f0431e.js","auth/email-otp.bd2609b21d97d9f0431e.js.map"\],"auth/escalate":\["auth/escalate.6615ed7953e111205e47.js","auth/escalate.6615ed7953e111205e47.js.map"\],"auth/forgot":\["auth/forgot.e78ed027ee95309fb126.js","auth/forgot.e78ed027ee95309fb126.js.map"\],"auth/forgot-sent":\["auth/forgot-sent.528609249775c4599a6b.js","auth/forgot-sent.528609249775c4599a6b.js.map"\],"auth/forgot-sent-anonymized":\["auth/forgot-sent-anonymized.9fa92466722083fafb6f.js","auth/forgot-sent-anonymized.9fa92466722083fafb6f.js.map"\],"auth/invite-signup":\["auth/invite-signup.b8873b64d4ff79edd325.js","auth/invite-signup.b8873b64d4ff79edd325.js.map"\],"auth/liminal-login-footer":\["auth/liminal-login-footer.a2b3deb090b5846e0245.js","auth/liminal-login-footer.a2b3deb090b5846e0245.js.map"\],"auth/login":\["auth/login.2bbe8272512a5fa13286.js","auth/login.2bbe8272512a5fa13286.js.map"\],"auth/otp":\["auth/otp.fac5b3260572be53fbdb.js","auth/otp.fac5b3260572be53fbdb.js.map"\],"auth/password":\["auth/password.df6d09005716bc5651a3.js","auth/password.df6d09005716bc5651a3.js.map"\],"auth/recovery-code":\["auth/recovery-code.843ae7ce584446c90177.js","auth/recovery-code.843ae7ce584446c90177.js.map"\],"auth/recovery-email-otp":\["auth/recovery-email-otp.4c5fdf800e9e40cb4d78.js","auth/recovery-email-otp.4c5fdf800e9e40cb4d78.js.map"\],"auth/reset-password":\["auth/reset-password.8175bdb0f6108b8b9a21.js","auth/reset-password.8175bdb0f6108b8b9a21.js.map"\],"auth/signup":\["auth/signup.5030f160c28e2897f8d5.js","auth/signup.5030f160c28e2897f8d5.js.map"\],"auth/test/email-otp-test":\["auth/test/email-otp-test.66b431ab26e08e627914.js","auth/test/email-otp-test.66b431ab26e08e627914.js.map"\],"auth/test/escalate-test":\["auth/test/escalate-test.1fd456d6dcee104717c4.js","auth/test/escalate-test.1fd456d6dcee104717c4.js.map"\],"auth/test/otp-test":\["auth/test/otp-test.cdfcb7100100d4747dea.js","auth/test/otp-test.cdfcb7100100d4747dea.js.map"\],"auth/test/webauthn-login-test":\["auth/test/webauthn-login-test.9b09975e569bb34da31f.js","auth/test/webauthn-login-test.9b09975e569bb34da31f.js.map"\],"auth/webauthn-cli-login":\["auth/webauthn-cli-login.2364b34b45db1e78685a.js","auth/webauthn-cli-login.2364b34b45db1e78685a.js.map"\],"auth/webauthn-login":\["auth/webauthn-login.d327dc165b44bd76696f.js","auth/webauthn-login.d327dc165b44bd76696f.js.map"\],"billing/delete-org":\["billing/delete-org.44182094e2e790868946.js","billing/delete-org.44182094e2e790868946.js.map"\],"billing/detail":\["billing/detail.2cbec5157d75788ac270.js","billing/detail.2cbec5157d75788ac270.js.map"\],"billing/downgrade":\["billing/downgrade.c64ed7780dc59a84ae06.js","billing/downgrade.c64ed7780dc59a84ae06.js.map"\],"billing/upgrade":\["billing/upgrade.de762291028db531fa5e.js","billing/upgrade.de762291028db531fa5e.js.map"\],"contact/contact":\["contact/contact.988235547d44be7f5930.js","contact/contact.988235547d44be7f5930.js.map"\],"contact/recovery-support-v1":\["contact/recovery-support-v1.cd6ecce4d65cb282f30c.js","contact/recovery-support-v1.cd6ecce4d65cb282f30c.js.map"\],"contact/recovery-support-v2":\["contact/recovery-support-v2.9176b5cb1c67ed716f4a.js","contact/recovery-support-v2.9176b5cb1c67ed716f4a.js.map"\],"contact/sidebar":\["contact/sidebar.0308aeefe5885a84d06b.js","contact/sidebar.0308aeefe5885a84d06b.js.map"\],"debug/badstatus":\["debug/badstatus.0488f063f31988658a28.js","debug/badstatus.0488f063f31988658a28.js.map"\],"debug/detail":\["debug/detail.cb7d0a34da61284eaff7.js","debug/detail.cb7d0a34da61284eaff7.js.map"\],"debug/failcomponent":\["debug/failcomponent.1cfacafad3f98b3d0aac.js","debug/failcomponent.1cfacafad3f98b3d0aac.js.map"\],"dev/choose-template":\["dev/choose-template.bdf6eea4986b65dba195.js","dev/choose-template.bdf6eea4986b65dba195.js.map"\],"dsr/dsr-export":\["dsr/dsr-export.d0872aed9e304fac45b3.js","dsr/dsr-export.d0872aed9e304fac45b3.js.map"\],"egg/egg":\["egg/egg.cfb0610f42fddd0406a9.js","egg/egg.cfb0610f42fddd0406a9.js.map"\],"errors/bad-request":\["errors/bad-request.a267501f2a1d8862d7b8.js","errors/bad-request.a267501f2a1d8862d7b8.js.map"\],"errors/not-found":\["errors/not-found.c05dacdc50d8bb138323.js","errors/not-found.c05dacdc50d8bb138323.js.map"\],"errors/server":\["errors/server.e360f3821de79d992e82.js","errors/server.e360f3821de79d992e82.js.map"\],"errors/tea-pot":\["errors/tea-pot.bee0e5d4c382490cc68f.js","errors/tea-pot.bee0e5d4c382490cc68f.js.map"\],"errors/template":\["errors/template.d5d92c32421e0fdb5b58.js","errors/template.d5d92c32421e0fdb5b58.js.map"\],"flatpage/flatpage":\["flatpage/flatpage.23bd20a0e264027e535c.js","flatpage/flatpage.23bd20a0e264027e535c.js.map"\],"homepage/homepage":\["homepage/homepage.91eece2b54cdecf07014.js","homepage/homepage.91eece2b54cdecf07014.js.map"\],"homepage/homepage-logged-in":\["homepage/homepage-logged-in.c3fe5085dbec84830625.js","homepage/homepage-logged-in.c3fe5085dbec84830625.js.map"\],"orgs/create":\["orgs/create.c7e483971135b2092f42.js","orgs/create.c7e483971135b2092f42.js.map"\],"orgs/detail":\["orgs/detail.c7c24782d424ae97b7cf.js","orgs/detail.c7c24782d424ae97b7cf.js.map"\],"orgs/invite":\["orgs/invite.bae1eb05984179ed6cf0.js","orgs/invite.bae1eb05984179ed6cf0.js.map"\],"orgs/tfa-enforced":\["orgs/tfa-enforced.09d23dd31204a6ab0c99.js","orgs/tfa-enforced.09d23dd31204a6ab0c99.js.map"\],"orgs/upgrade":\["orgs/upgrade.c61ad67e11638ab94182.js","orgs/upgrade.c61ad67e11638ab94182.js.map"\],"package-list/package-list":\["package-list/package-list.df1e1f68bef734cc8959.js","package-list/package-list.df1e1f68bef734cc8959.js.map"\],"package/delete-package":\["package/delete-package.6f2c19e7f24e8535a590.js","package/delete-package.6f2c19e7f24e8535a590.js.map"\],"package/deprecate-package":\["package/deprecate-package.88b41151d56dc340e4cb.js","package/deprecate-package.88b41151d56dc340e4cb.js.map"\],"package/error-message-package":\["package/error-message-package.0272a3fa7a715cd5f324.js","package/error-message-package.0272a3fa7a715cd5f324.js.map"\],"package/package":\["package/package.3a506170b8f61bd3182d.js","package/package.3a506170b8f61bd3182d.js.map"\],"profile/profile":\["profile/profile.664e252c73df8728169b.js","profile/profile.664e252c73df8728169b.js.map"\],"recovery-codes/show":\["recovery-codes/show.c657f40276130cd3df6e.js","recovery-codes/show.c657f40276130cd3df6e.js.map"\],"search/search":\["search/search.e48d0fe560f309601f24.js","search/search.e48d0fe560f309601f24.js.map"\],"searchv2/search":\["searchv2/search.f0c99cee3f35b75815f9.js","searchv2/search.f0c99cee3f35b75815f9.js.map"\],"settings/change-password":\["settings/change-password.67853738fd84f0a27778.js","settings/change-password.67853738fd84f0a27778.js.map"\],"settings/delete-account":\["settings/delete-account.5199237026d0b49d9ff4.js","settings/delete-account.5199237026d0b49d9ff4.js.map"\],"settings/email":\["settings/email.879c0f095b9daeacb691.js","settings/email.879c0f095b9daeacb691.js.map"\],"settings/memberships":\["settings/memberships.d0f2fe757fde7fab4ee8.js","settings/memberships.d0f2fe757fde7fab4ee8.js.map"\],"settings/packages":\["settings/packages.ddf4d6bb5378e8b4cdfd.js","settings/packages.ddf4d6bb5378e8b4cdfd.js.map"\],"settings/profile":\["settings/profile.440c36c849f558a1a9da.js","settings/profile.440c36c849f558a1a9da.js.map"\],"teams/create":\["teams/create.f5c8857f610d05bfe241.js","teams/create.f5c8857f610d05bfe241.js.map"\],"teams/detail":\["teams/detail.7ffa2d1ec5ad7114f42c.js","teams/detail.7ffa2d1ec5ad7114f42c.js.map"\],"teams/list":\["teams/list.cb43226fb34c7e212846.js","teams/list.cb43226fb34c7e212846.js.map"\],"teams/packages":\["teams/packages.00ac60e3b10b1e056fb0.js","teams/packages.00ac60e3b10b1e056fb0.js.map"\],"teams/users":\["teams/users.288db5d5133c9fad4fe6.js","teams/users.288db5d5133c9fad4fe6.js.map"\],"tfa/additional-options":\["tfa/additional-options.8e4f1e561843b3ea7167.js","tfa/additional-options.8e4f1e561843b3ea7167.js.map"\],"tfa/enable":\["tfa/enable.b17e1b67978606d55916.js","tfa/enable.b17e1b67978606d55916.js.map"\],"tfa/list":\["tfa/list.cb2d4f544e77157976ed.js","tfa/list.cb2d4f544e77157976ed.js.map"\],"tfa/recovery-codes":\["tfa/recovery-codes.aa1c339a0d3617cba165.js","tfa/recovery-codes.aa1c339a0d3617cba165.js.map"\],"tfa/security-key-list":\["tfa/security-key-list.4aa1d3c8ed69e55fc63a.js","tfa/security-key-list.4aa1d3c8ed69e55fc63a.js.map"\],"tfa/showTFAQRCode":\["tfa/showTFAQRCode.d22958b27ad9439d9207.js","tfa/showTFAQRCode.d22958b27ad9439d9207.js.map"\],"tfa/showTFASuccess":\["tfa/showTFASuccess.e2f586018fe2d849ab1d.js","tfa/showTFASuccess.e2f586018fe2d849ab1d.js.map"\],"tfa/tfa-disable":\["tfa/tfa-disable.fa0806fc68d409503a76.js","tfa/tfa-disable.fa0806fc68d409503a76.js.map"\],"tfa/tfa-mode-selection":\["tfa/tfa-mode-selection.4cba5a5006d207ef9d95.js","tfa/tfa-mode-selection.4cba5a5006d207ef9d95.js.map"\],"tfa/tfa-password-entry":\["tfa/tfa-password-entry.3e1370b44f4d205f05b9.js","tfa/tfa-password-entry.3e1370b44f4d205f05b9.js.map"\],"tfa/totp":\["tfa/totp.19eec4bb703c042d3b38.js","tfa/totp.19eec4bb703c042d3b38.js.map"\],"tokens/create":\["tokens/create.ced58efc306e37040364.js","tokens/create.ced58efc306e37040364.js.map"\],"tokens/create-gat":\["tokens/create-gat.71a6134e1b02c8379360.js","tokens/create-gat.71a6134e1b02c8379360.js.map"\],"tokens/gat-details":\["tokens/gat-details.841c6f6b1d2094180b40.js","tokens/gat-details.841c6f6b1d2094180b40.js.map"\],"tokens/gat/allowed-ip-ranges-input":\["tokens/gat/allowed-ip-ranges-input.240c0eb7bff703cda341.js","tokens/gat/allowed-ip-ranges-input.240c0eb7bff703cda341.js.map"\],"tokens/gat/constants":\["tokens/gat/constants.e0e048f94ba517582ef2.js","tokens/gat/constants.e0e048f94ba517582ef2.js.map"\],"tokens/gat/divider":\["tokens/gat/divider.552c2e7547bbdab1e6ed.js","tokens/gat/divider.552c2e7547bbdab1e6ed.js.map"\],"tokens/gat/expiration-days-input":\["tokens/gat/expiration-days-input.da944cb892332cb8f521.js","tokens/gat/expiration-days-input.da944cb892332cb8f521.js.map"\],"tokens/gat/gat-expiration":\["tokens/gat/gat-expiration.04251fbd5c533e5fdf0c.js","tokens/gat/gat-expiration.04251fbd5c533e5fdf0c.js.map"\],"tokens/gat/gat-input":\["tokens/gat/gat-input.5cd4a760eff0f2e5f786.js","tokens/gat/gat-input.5cd4a760eff0f2e5f786.js.map"\],"tokens/gat/organizations-input":\["tokens/gat/organizations-input.4bf1e742ef14b9366141.js","tokens/gat/organizations-input.4bf1e742ef14b9366141.js.map"\],"tokens/gat/organizations-section":\["tokens/gat/organizations-section.36d4df0723b2570fbcc7.js","tokens/gat/organizations-section.36d4df0723b2570fbcc7.js.map"\],"tokens/gat/packages-and-scopes-input":\["tokens/gat/packages-and-scopes-input.7aee5a140609634d9618.js","tokens/gat/packages-and-scopes-input.7aee5a140609634d9618.js.map"\],"tokens/gat/packages-and-scopes-section":\["tokens/gat/packages-and-scopes-section.61b79e58615de88ce1c7.js","tokens/gat/packages-and-scopes-section.61b79e58615de88ce1c7.js.map"\],"tokens/gat/permissions-input":\["tokens/gat/permissions-input.70bce7222acff9a6618b.js","tokens/gat/permissions-input.70bce7222acff9a6618b.js.map"\],"tokens/gat/section":\["tokens/gat/section.4b1c4a7d483929ae9bf9.js","tokens/gat/section.4b1c4a7d483929ae9bf9.js.map"\],"tokens/gat/section-heading":\["tokens/gat/section-heading.bc330b60cca9b5de71e0.js","tokens/gat/section-heading.bc330b60cca9b5de71e0.js.map"\],"tokens/gat/selected-items":\["tokens/gat/selected-items.e521a47d8d297fd3211e.js","tokens/gat/selected-items.e521a47d8d297fd3211e.js.map"\],"tokens/gat/utils":\["tokens/gat/utils.2a098685d862a6ee787b.js","tokens/gat/utils.2a098685d862a6ee787b.js.map"\],"tokens/generate-token":\["tokens/generate-token.5f82c99ce1fc18b831c1.js","tokens/generate-token.5f82c99ce1fc18b831c1.js.map"\],"tokens/list":\["tokens/list.c12328ee7274bcd8ccdc.js","tokens/list.c12328ee7274bcd8ccdc.js.map"\],"vouchers/view":\["vouchers/view.341dbbf9a6d4dce70ec7.js","vouchers/view.341dbbf9a6d4dce70ec7.js.map"\]},"hash":"864e8e8e5aa9c8980866","name":"package/package","containerId":"app","headerName":"x-spiferack","publicPath":"https://static-production.npmjs.com/"}